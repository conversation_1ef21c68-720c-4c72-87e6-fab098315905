{"openapi": "3.0.1", "info": {"title": "RBAC.Write.Api", "version": "1.0"}, "paths": {"/api/RBAC/CreatePermission": {"post": {"tags": ["RBAC"], "summary": "添加权限", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePermissionCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePermissionCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePermissionCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}}}}}}, "/api/RBAC/CreateRoleC": {"post": {"tags": ["RBAC"], "summary": "创建角色", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRoleCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateRoleCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateRoleCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}}}}}}, "/api/RBAC/CreateUser": {"post": {"tags": ["RBAC"], "summary": "创建用户", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}}}}}}, "/api/RBAC/UpdatePermission": {"put": {"tags": ["RBAC"], "summary": "修改权限", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}}}}}}, "/api/RBAC/UpdateRole": {"put": {"tags": ["RBAC"], "summary": "更新角色", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateRoleCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}}}}}}, "/api/RBAC/UpdateUser": {"put": {"tags": ["RBAC"], "summary": "更新用户", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}}}}}}, "/api/RBAC/DeletePermission": {"delete": {"tags": ["RBAC"], "summary": "删除权限", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeletePermissionCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeletePermissionCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeletePermissionCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}}}}}}, "/api/RBAC/DeleteRole": {"delete": {"tags": ["RBAC"], "summary": "删除角色", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteRoleCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteRoleCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteRoleCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}}}}}}, "/api/RBAC/DeleteUser": {"delete": {"tags": ["RBAC"], "summary": "删除用户", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteUserCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteUserCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteUserCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}}}}}}, "/api/RBAC/UpdateRoleEnable": {"put": {"tags": ["RBAC"], "summary": "修改角色状态", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleEnableCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleEnableCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateRoleEnableCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}}}}}}, "/api/RBAC/UpdateUserEnable": {"put": {"tags": ["RBAC"], "summary": "/修改用户状态", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserEnableCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserEnableCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserEnableCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}}}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"APIEnums": {"enum": [200, 500, 502], "type": "integer", "format": "int32"}, "CreatePermissionCommand": {"type": "object", "properties": {"permissionName": {"type": "string", "description": "权限名称", "nullable": true}, "permissionURL": {"type": "string", "description": "权限URL", "nullable": true}, "parentId": {"type": "integer", "description": "父级权限ID", "format": "int64"}}, "additionalProperties": false}, "CreateRoleCommand": {"type": "object", "properties": {"roleName": {"type": "string", "description": "角色名称", "nullable": true}, "isEnable": {"type": "boolean", "description": "是否启用"}, "permissionIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "权限Ids", "nullable": true}}, "additionalProperties": false}, "CreateUserCommand": {"type": "object", "properties": {"workNumber": {"type": "string", "description": "工号", "nullable": true}, "userName": {"type": "string", "description": "用户名", "nullable": true}, "password": {"type": "string", "description": "用户密码", "nullable": true}, "fullName": {"type": "string", "description": "用户姓名", "nullable": true}, "sex": {"type": "boolean", "description": "性别  true 男  false 女"}, "address": {"type": "string", "description": "地址", "nullable": true}, "mobile": {"type": "string", "description": "手机号码", "nullable": true}, "renPing": {"type": "string", "description": "拼音缩写", "nullable": true}, "departmentId": {"type": "integer", "description": "科室名称", "format": "int64"}, "registrationFee": {"type": "integer", "description": "挂号费", "format": "int32", "nullable": true}, "isEnable": {"type": "boolean", "description": "是否启用"}, "roleId": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "角色Id", "nullable": true}}, "additionalProperties": false}, "DeletePermissionCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "DeleteRoleCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "DeleteUserCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "Int32APIResult": {"type": "object", "properties": {"msg": {"type": "string", "nullable": true}, "code": {"$ref": "#/components/schemas/APIEnums"}, "data": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdatePermissionCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "permissionName": {"type": "string", "description": "权限名称", "nullable": true}, "permissionURL": {"type": "string", "description": "权限URL", "nullable": true}, "parentId": {"type": "integer", "description": "父级权限ID", "format": "int64"}}, "additionalProperties": false}, "UpdateRoleCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "roleName": {"type": "string", "description": "角色名称", "nullable": true}, "isEnable": {"type": "boolean", "description": "是否启用"}, "permissionIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "权限Ids", "nullable": true}}, "additionalProperties": false}, "UpdateRoleEnableCommand": {"type": "object", "properties": {"id": {"type": "integer", "description": "角色Id", "format": "int64"}, "isEnable": {"type": "boolean", "description": "是否启用"}}, "additionalProperties": false}, "UpdateUserCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "workNumber": {"type": "string", "description": "工号", "nullable": true}, "userName": {"type": "string", "description": "用户名", "nullable": true}, "password": {"type": "string", "description": "用户密码", "nullable": true}, "fullName": {"type": "string", "description": "用户姓名", "nullable": true}, "sex": {"type": "boolean", "description": "性别  true 男  false 女"}, "address": {"type": "string", "description": "地址", "nullable": true}, "mobile": {"type": "string", "description": "手机号码", "nullable": true}, "renPing": {"type": "string", "description": "拼音缩写", "nullable": true}, "departmentId": {"type": "integer", "description": "科室名称", "format": "int64"}, "registrationFee": {"type": "integer", "description": "挂号费", "format": "int32", "nullable": true}, "isEnable": {"type": "boolean", "description": "是否启用"}, "roleId": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "角色Id", "nullable": true}}, "additionalProperties": false}, "UpdateUserEnableCommand": {"type": "object", "properties": {"id": {"type": "integer", "description": "用户Id", "format": "int64"}, "isEnable": {"type": "boolean", "description": "是否启用"}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}