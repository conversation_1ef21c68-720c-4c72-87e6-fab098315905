{"Version": 1, "Hash": "2cQpxVySfRkWPh+pt005Ck/5mCLPUPMcVE6Ye9Q4DxQ=", "Source": "SmartHealthcare.Read.Api", "BasePath": "_content/SmartHealthcare.Read.Api", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "SmartHealthcare.Read.Api\\wwwroot", "Source": "SmartHealthcare.Read.Api", "ContentRoot": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\", "BasePath": "_content/SmartHealthcare.Read.Api", "Pattern": "**"}], "Assets": [{"Identity": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\department-case-demo.html", "SourceId": "SmartHealthcare.Read.Api", "SourceType": "Discovered", "ContentRoot": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\", "BasePath": "_content/SmartHealthcare.Read.Api", "RelativePath": "department-case-demo#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zvet8wci5s", "Integrity": "YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\department-case-demo.html", "FileLength": 8141, "LastWriteTime": "2025-07-30T13:03:12+00:00"}, {"Identity": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\department-case-interactive.html", "SourceId": "SmartHealthcare.Read.Api", "SourceType": "Discovered", "ContentRoot": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\", "BasePath": "_content/SmartHealthcare.Read.Api", "RelativePath": "department-case-interactive#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xeaab6lj4y", "Integrity": "OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\department-case-interactive.html", "FileLength": 17964, "LastWriteTime": "2025-08-01T01:02:19+00:00"}], "Endpoints": [{"Route": "department-case-demo.html", "AssetFile": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\department-case-demo.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8141"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI=\""}, {"Name": "Last-Modified", "Value": "Wed, 30 Jul 2025 13:03:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI="}]}, {"Route": "department-case-demo.zvet8wci5s.html", "AssetFile": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\department-case-demo.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8141"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI=\""}, {"Name": "Last-Modified", "Value": "Wed, 30 Jul 2025 13:03:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zvet8wci5s"}, {"Name": "label", "Value": "department-case-demo.html"}, {"Name": "integrity", "Value": "sha256-YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI="}]}, {"Route": "department-case-interactive.html", "AssetFile": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\department-case-interactive.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17964"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 01 Aug 2025 01:02:19 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE="}]}, {"Route": "department-case-interactive.xeaab6lj4y.html", "AssetFile": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\department-case-interactive.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17964"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 01 Aug 2025 01:02:19 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xeaab6lj4y"}, {"Name": "label", "Value": "department-case-interactive.html"}, {"Name": "integrity", "Value": "sha256-OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE="}]}]}