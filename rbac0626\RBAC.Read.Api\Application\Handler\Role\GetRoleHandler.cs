﻿using AutoMapper;
using MediatR;
using RBAC.Domain.Dto.Output;
using RBAC.Domain.RBAC;
using RBAC.ErrorCode;
using RBAC.Interstructrue.Base;
using RBAC.Read.Api.Application.Command.Role;

namespace RBAC.Read.Api.Application.Handler.Role
{
    public class GetRoleHandler : IRequestHandler<GetRoleCommand, APIResult<List<GetRoleListDto>>>
    {
        private readonly IBaseRepository<RoleModel> roleRepository;
        private readonly IMapper mapper;
        private readonly ILogger<GetRoleHandler> logger;

        public GetRoleHandler(IBaseRepository<RoleModel> roleRepository, IMapper mapper, ILogger<GetRoleHandler> logger)
        {
            this.roleRepository = roleRepository;
            this.mapper = mapper;
            this.logger = logger;
        }
        /// <summary>
        /// 获取角色列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public Task<APIResult<List<GetRoleListDto>>> Handle(GetRoleCommand request, CancellationToken cancellationToken)
        {
            APIResult<List<GetRoleListDto>> res = new APIResult<List<GetRoleListDto>>();
            var model = roleRepository.GetALL().Where(x => x.IsDeleted == false);
            var data = mapper.Map<List<GetRoleListDto>>(model);
            res.Data = data;
            res.Code = APIEnums.Success;
            res.Msg = "查询成功";
            logger.LogInformation("查询成功");
            return Task.FromResult(res);
        }
    }
}
