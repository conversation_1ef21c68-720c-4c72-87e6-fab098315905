{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "department-case-demo.html", "AssetFile": "department-case-demo.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8141"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI=\""}, {"Name": "Last-Modified", "Value": "Wed, 30 Jul 2025 13:03:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI="}]}, {"Route": "department-case-demo.zvet8wci5s.html", "AssetFile": "department-case-demo.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8141"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI=\""}, {"Name": "Last-Modified", "Value": "Wed, 30 Jul 2025 13:03:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zvet8wci5s"}, {"Name": "integrity", "Value": "sha256-YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI="}, {"Name": "label", "Value": "department-case-demo.html"}]}, {"Route": "department-case-interactive.html", "AssetFile": "department-case-interactive.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17964"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 01 Aug 2025 01:02:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE="}]}, {"Route": "department-case-interactive.xeaab6lj4y.html", "AssetFile": "department-case-interactive.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "17964"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 01 Aug 2025 01:02:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xeaab6lj4y"}, {"Name": "integrity", "Value": "sha256-OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE="}, {"Name": "label", "Value": "department-case-interactive.html"}]}]}