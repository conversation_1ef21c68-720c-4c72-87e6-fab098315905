<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科室病例管理</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            display: flex;
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .sidebar {
            width: 300px;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
            padding: 20px;
        }
        .main-content {
            flex: 1;
            padding: 20px;
        }
        .department-item {
            padding: 12px;
            margin-bottom: 8px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: white;
        }
        .department-item:hover {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }
        .department-item.active {
            background-color: #2196f3;
            color: white;
            border-color: #2196f3;
        }
        .department-name {
            font-weight: bold;
            margin-bottom: 4px;
        }
        .department-info {
            font-size: 12px;
            color: #666;
        }
        .department-item.active .department-info {
            color: #e3f2fd;
        }
        .case-count {
            float: right;
            background-color: #ff9800;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .department-item.active .case-count {
            background-color: rgba(255,255,255,0.3);
        }
        .case-list {
            margin-top: 20px;
        }
        .case-item {
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        .case-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .case-info {
            font-size: 12px;
            color: #666;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error {
            color: #dc3545;
            text-align: center;
            padding: 20px;
        }
        .header {
            background-color: #2196f3;
            color: white;
            padding: 15px 20px;
            margin: -20px -20px 20px -20px;
        }
        .no-cases {
            text-align: center;
            color: #666;
            padding: 40px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="header">
                <h3 style="margin: 0;">科室列表</h3>
            </div>
            <div id="departmentList" class="loading">加载中...</div>
        </div>
        <div class="main-content">
            <div class="header">
                <h3 style="margin: 0;">病例信息</h3>
            </div>
            <div id="caseList">
                <div class="no-cases">请选择左侧科室查看病例信息</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = '/api/SmartHealthcare';
        let departments = [];
        let selectedDepartmentId = null;

        // 加载科室列表
        async function loadDepartments() {
            try {
                const response = await fetch(`${API_BASE_URL}/GetDepartmentWithCases`);
                const result = await response.json();
                
                if (result.code === 200) {
                    departments = result.data;
                    renderDepartmentList();
                } else {
                    document.getElementById('departmentList').innerHTML = 
                        `<div class="error">加载科室列表失败: ${result.msg}</div>`;
                }
            } catch (error) {
                console.error('加载科室列表失败:', error);
                document.getElementById('departmentList').innerHTML = 
                    '<div class="error">网络错误，请检查连接</div>';
            }
        }

        // 渲染科室列表
        function renderDepartmentList() {
            const container = document.getElementById('departmentList');
            
            if (departments.length === 0) {
                container.innerHTML = '<div class="no-cases">暂无科室数据</div>';
                return;
            }

            const html = departments.map(dept => `
                <div class="department-item" onclick="selectDepartment(${dept.id})" data-id="${dept.id}">
                    <div class="department-name">
                        ${dept.departmentName}
                        <span class="case-count">${dept.caseCount}</span>
                    </div>
                    <div class="department-info">
                        编码: ${dept.departmentCode} | 地址: ${dept.address}
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // 选择科室
        async function selectDepartment(departmentId) {
            // 更新选中状态
            document.querySelectorAll('.department-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-id="${departmentId}"]`).classList.add('active');
            
            selectedDepartmentId = departmentId;
            
            // 加载该科室的病例
            await loadCasesByDepartment(departmentId);
        }

        // 加载指定科室的病例
        async function loadCasesByDepartment(departmentId) {
            const caseListContainer = document.getElementById('caseList');
            caseListContainer.innerHTML = '<div class="loading">加载中...</div>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/GetCasesByDepartment?departmentId=${departmentId}`);
                const result = await response.json();
                
                if (result.code === 200) {
                    renderCaseList(result.data);
                } else {
                    caseListContainer.innerHTML = 
                        `<div class="error">加载病例失败: ${result.msg}</div>`;
                }
            } catch (error) {
                console.error('加载病例失败:', error);
                caseListContainer.innerHTML = 
                    '<div class="error">网络错误，请检查连接</div>';
            }
        }

        // 渲染病例列表
        function renderCaseList(cases) {
            const container = document.getElementById('caseList');
            
            if (cases.length === 0) {
                container.innerHTML = '<div class="no-cases">该科室暂无病例</div>';
                return;
            }

            const html = cases.map(caseItem => `
                <div class="case-item">
                    <div class="case-name">${caseItem.caseName}</div>
                    <div class="case-info">
                        病例ID: ${caseItem.id} | 
                        创建时间: ${new Date(caseItem.createTime).toLocaleString()} | 
                        创建人: ${caseItem.createUser}
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDepartments();
        });
    </script>
</body>
</html>
