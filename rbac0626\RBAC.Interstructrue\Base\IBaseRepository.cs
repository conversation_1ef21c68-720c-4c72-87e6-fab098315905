﻿using RBAC.Domain;

namespace RBAC.Interstructrue.Base
{
    public interface IBaseRepository<T> where T : BaseEntity
    {
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        int Create(T entity);
        /// <summary>
        /// 修改 + 删除
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        int Update(T entity);
        /// <summary>
        /// 通过ID获取数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        T GetModel(long id);
        /// <summary>
        ///通过表单时式获取数据
        /// </summary>
        /// <returns></returns>
        IQueryable<T> GetALL();

    }
}
