﻿using AutoMapper;
using MediatR;
using RBAC.Domain.SmartHealthcare;
using RBAC.ErrorCode;
using RBAC.Interstructrue.Base;
using SmartHealthcare.Write.Api.Application.Command.Department;

namespace SmartHealthcare.Write.Api.Application.Handler.Department
{
    public class DeleteDepartmentHandler : IRequestHandler<DeleteDepartmentCommand, APIResult<int>>
    {
        private readonly IBaseRepository<DepartmentModel> departRepository;
        private readonly IMapper mapper;
        private readonly ILogger<DeleteDepartmentHandler> logger;

        public DeleteDepartmentHandler(IBaseRepository<DepartmentModel> departRepository, IMapper mapper, ILogger<DeleteDepartmentHandler> logger)
        {
            this.departRepository = departRepository;
            this.mapper = mapper;
            this.logger = logger;
        }
        /// <summary>
        /// 删除科室
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public Task<APIResult<int>> Handle(DeleteDepartmentCommand request, CancellationToken cancellationToken)
        {
            APIResult<int> res = new APIResult<int>();
            var model = departRepository.GetModel(request.Id);
            if (model != null)
            {
                model.IsDeleted = true;
                var r = departRepository.Update(model);
                if (r > 0)
                {
                    res.Data = r;
                    res.Code = APIEnums.Success;
                    res.Msg = "删除科室成功";
                    logger.LogInformation("删除科室成功");
                    return Task.FromResult(res);
                }
                else
                {
                    res.Data = r;
                    res.Code = APIEnums.Fail;
                    res.Msg = "删除科室失败";
                    logger.LogError("删除科室失败");
                    return Task.FromResult(res);
                }
            }
            else
            {
                res.Data = 0;
                res.Code = APIEnums.Fail;
                res.Msg = "您要删除的科室不存在";
                logger.LogError("删除科室失败");
                return Task.FromResult(res);
            }
        }
    }
}
