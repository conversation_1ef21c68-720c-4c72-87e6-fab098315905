using IGeekFan.AspNetCore.Knife4jUI;
using Microsoft.EntityFrameworkCore;
using NLog.Web;
using RBAC.Interstructrue;
using RBAC.Interstructrue.Base;
using SmartHealthcare.Read.Api;
using System.Reflection;
using Yitter.IdGenerator;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
// ���ݿ�����
builder.Services.AddDbContext<RBACDbContext>(x =>
{
    x.UseMySql(builder.Configuration.GetConnectionString("MySQL"), new MySqlServerVersion("5.7"));
});
//�н���
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));
// �Զ�ӳ��
builder.Services.AddAutoMapper(typeof(SmartHealthcareAutoMapper));
// ��־
builder.Host.UseNLog();
// ѩ��id
YitIdHelper.SetIdGenerator(new IdGeneratorOptions());
// �ִ�
builder.Services.AddScoped(typeof(IBaseRepository<>), typeof(BaseRepository<>));

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    //app.UseSwaggerUI();
    app.UseKnife4UI();
}

// 启用静态文件服务
app.UseStaticFiles();

app.UseAuthorization();

app.MapControllers();

app.Run();
