{"openapi": "3.0.1", "info": {"title": "RBAC.Read.Api", "version": "1.0"}, "paths": {"/api/RBAC/Login": {"get": {"tags": ["RBAC"], "summary": "登录", "parameters": [{"name": "UserName", "in": "query", "description": "用户名", "schema": {"type": "string"}}, {"name": "Password", "in": "query", "description": "密码", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoAPIResult"}}}}}}}, "/api/RBAC/GetPermission": {"get": {"tags": ["RBAC"], "summary": "获取权限列表", "parameters": [{"name": "request", "in": "query", "description": "", "schema": {"$ref": "#/components/schemas/GetPermissionCommand"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetPermissionListDtoListAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetPermissionListDtoListAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetPermissionListDtoListAPIResult"}}}}}}}, "/api/RBAC/GetPermissionList": {"get": {"tags": ["RBAC"], "summary": "获取权限列表", "parameters": [{"name": "PageIndex", "in": "query", "description": "页码", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "description": "每页数量", "schema": {"type": "integer", "format": "int32"}}, {"name": "PermissionName", "in": "query", "description": "权限名称", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PermissionDtoAPIPaging"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PermissionDtoAPIPaging"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PermissionDtoAPIPaging"}}}}}}}, "/api/RBAC/GetRoleList": {"get": {"tags": ["RBAC"], "summary": "获取角色列表", "parameters": [{"name": "PageIndex", "in": "query", "description": "页码", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "description": "每页数量", "schema": {"type": "integer", "format": "int32"}}, {"name": "RoleName", "in": "query", "description": "角色名称", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetRoleListDtoAPIPaging"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetRoleListDtoAPIPaging"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetRoleListDtoAPIPaging"}}}}}}}, "/api/RBAC/GetUserList": {"get": {"tags": ["RBAC"], "summary": "获取用户列表", "parameters": [{"name": "Pageindex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserName", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetUserListDtoAPIPaging"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetUserListDtoAPIPaging"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetUserListDtoAPIPaging"}}}}}}}, "/api/RBAC/GetRole": {"get": {"tags": ["RBAC"], "summary": "获取角色列表", "parameters": [{"name": "request", "in": "query", "description": "", "schema": {"$ref": "#/components/schemas/GetRoleCommand"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetRoleListDtoListAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetRoleListDtoListAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetRoleListDtoListAPIResult"}}}}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"APIEnums": {"enum": [200, 500, 502], "type": "integer", "format": "int32"}, "GetPermissionCommand": {"type": "object", "additionalProperties": false}, "GetPermissionListDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "permissionName": {"type": "string", "nullable": true}, "permissionURL": {"type": "string", "nullable": true}, "parentId": {"type": "integer", "format": "int64"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/GetPermissionListDto"}, "nullable": true}}, "additionalProperties": false}, "GetPermissionListDtoListAPIResult": {"type": "object", "properties": {"msg": {"type": "string", "nullable": true}, "code": {"$ref": "#/components/schemas/APIEnums"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GetPermissionListDto"}, "nullable": true}}, "additionalProperties": false}, "GetRoleCommand": {"type": "object", "additionalProperties": false}, "GetRoleListDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "roleId": {"type": "integer", "format": "int64"}, "roleName": {"type": "string", "nullable": true}, "permissionIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "permissionNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "isEnable": {"type": "boolean"}}, "additionalProperties": false}, "GetRoleListDtoAPIPaging": {"type": "object", "properties": {"msg": {"type": "string", "nullable": true}, "code": {"$ref": "#/components/schemas/APIEnums"}, "data": {"$ref": "#/components/schemas/GetRoleListDto"}, "totalCount": {"type": "integer", "format": "int32"}, "pageDatas": {"type": "array", "items": {"$ref": "#/components/schemas/GetRoleListDto"}, "nullable": true}}, "additionalProperties": false}, "GetRoleListDtoListAPIResult": {"type": "object", "properties": {"msg": {"type": "string", "nullable": true}, "code": {"$ref": "#/components/schemas/APIEnums"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GetRoleListDto"}, "nullable": true}}, "additionalProperties": false}, "GetUserListDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "workNumber": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "sex": {"type": "boolean"}, "address": {"type": "string", "nullable": true}, "mobile": {"type": "string", "nullable": true}, "renPing": {"type": "string", "nullable": true}, "departmentId": {"type": "integer", "format": "int64"}, "registrationFee": {"type": "integer", "format": "int32", "nullable": true}, "roleId": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "roleName": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isEnable": {"type": "boolean"}}, "additionalProperties": false}, "GetUserListDtoAPIPaging": {"type": "object", "properties": {"msg": {"type": "string", "nullable": true}, "code": {"$ref": "#/components/schemas/APIEnums"}, "data": {"$ref": "#/components/schemas/GetUserListDto"}, "totalCount": {"type": "integer", "format": "int32"}, "pageDatas": {"type": "array", "items": {"$ref": "#/components/schemas/GetUserListDto"}, "nullable": true}}, "additionalProperties": false}, "PermissionDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "permissionName": {"type": "string", "nullable": true}, "permissionURL": {"type": "string", "nullable": true}, "parentId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "PermissionDtoAPIPaging": {"type": "object", "properties": {"msg": {"type": "string", "nullable": true}, "code": {"$ref": "#/components/schemas/APIEnums"}, "data": {"$ref": "#/components/schemas/PermissionDto"}, "totalCount": {"type": "integer", "format": "int32"}, "pageDatas": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionDto"}, "nullable": true}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "workNumber": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "sex": {"type": "boolean"}, "address": {"type": "string", "nullable": true}, "mobile": {"type": "string", "nullable": true}, "renPing": {"type": "string", "nullable": true}, "departmentId": {"type": "integer", "format": "int64"}, "registrationFee": {"type": "integer", "format": "int32", "nullable": true}, "isEnable": {"type": "boolean"}, "roleId": {"type": "integer", "format": "int64"}, "roleName": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserDtoAPIResult": {"type": "object", "properties": {"msg": {"type": "string", "nullable": true}, "code": {"$ref": "#/components/schemas/APIEnums"}, "data": {"$ref": "#/components/schemas/UserDto"}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}