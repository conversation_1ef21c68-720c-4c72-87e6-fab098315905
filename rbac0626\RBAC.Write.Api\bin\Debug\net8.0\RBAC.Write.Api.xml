<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RBAC.Write.Api</name>
    </assembly>
    <members>
        <member name="P:RBAC.Write.Api.Application.Command.Permission.CreatePermissionCommand.PermissionName">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.Permission.CreatePermissionCommand.PermissionURL">
            <summary>
            权限URL
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.Permission.CreatePermissionCommand.ParentId">
            <summary>
            父级权限ID
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.Permission.UpdatePermissionCommand.PermissionName">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.Permission.UpdatePermissionCommand.PermissionURL">
            <summary>
            权限URL
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.Permission.UpdatePermissionCommand.ParentId">
            <summary>
            父级权限ID
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.Role.CreateRoleCommand.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.Role.CreateRoleCommand.IsEnable">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.Role.CreateRoleCommand.PermissionIds">
            <summary>
            权限Ids 
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.Role.UpdateRoleCommand.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.Role.UpdateRoleCommand.IsEnable">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.Role.UpdateRoleCommand.PermissionIds">
            <summary>
            权限Ids 
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.Role.UpdateRoleEnableCommand.Id">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.Role.UpdateRoleEnableCommand.IsEnable">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.CreateUserCommand.WorkNumber">
            <summary>
            工号
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.CreateUserCommand.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.CreateUserCommand.Password">
            <summary>
            用户密码
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.CreateUserCommand.FullName">
             <summary>
            用户姓名
             </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.CreateUserCommand.Sex">
            <summary>
            性别  true 男  false 女
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.CreateUserCommand.Address">
            <summary>
            地址
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.CreateUserCommand.Mobile">
            <summary>
            手机号码
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.CreateUserCommand.RenPing">
            <summary>
            拼音缩写
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.CreateUserCommand.DepartmentId">
             <summary>
            科室名称
             </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.CreateUserCommand.RegistrationFee">
            <summary>
            挂号费
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.CreateUserCommand.IsEnable">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.CreateUserCommand.RoleId">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.UpdateUserCommand.WorkNumber">
            <summary>
            工号
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.UpdateUserCommand.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.UpdateUserCommand.Password">
            <summary>
            用户密码
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.UpdateUserCommand.FullName">
             <summary>
            用户姓名
             </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.UpdateUserCommand.Sex">
            <summary>
            性别  true 男  false 女
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.UpdateUserCommand.Address">
            <summary>
            地址
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.UpdateUserCommand.Mobile">
            <summary>
            手机号码
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.UpdateUserCommand.RenPing">
            <summary>
            拼音缩写
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.UpdateUserCommand.DepartmentId">
             <summary>
            科室名称
             </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.UpdateUserCommand.RegistrationFee">
            <summary>
            挂号费
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.UpdateUserCommand.IsEnable">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.UpdateUserCommand.RoleId">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.UpdateUserEnableCommand.Id">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="P:RBAC.Write.Api.Application.Command.User.UpdateUserEnableCommand.IsEnable">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="M:RBAC.Write.Api.Application.Handler.Permission.CreatePermissionHandler.Handle(RBAC.Write.Api.Application.Command.Permission.CreatePermissionCommand,System.Threading.CancellationToken)">
            <summary>
            添加权限
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Application.Handler.Permission.DeletePermissionHandler.Handle(RBAC.Write.Api.Application.Command.Permission.DeletePermissionCommand,System.Threading.CancellationToken)">
            <summary>
            删除权限
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Application.Handler.Permission.UpdatePermissionHandler.Handle(RBAC.Write.Api.Application.Command.Permission.UpdatePermissionCommand,System.Threading.CancellationToken)">
            <summary>
            修改权限
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Application.Handler.Role.CreateRoleHandler.Handle(RBAC.Write.Api.Application.Command.Role.CreateRoleCommand,System.Threading.CancellationToken)">
            <summary>
            创建角色
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Application.Handler.Role.DeleteRoleHandler.Handle(RBAC.Write.Api.Application.Command.Role.DeleteRoleCommand,System.Threading.CancellationToken)">
            <summary>
            删除角色
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Application.Handler.Role.UpdateRoleEnableHandler.Handle(RBAC.Write.Api.Application.Command.Role.UpdateRoleEnableCommand,System.Threading.CancellationToken)">
            <summary>
            修改角色状态
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Application.Handler.Role.UpdateRoleHandler.Handle(RBAC.Write.Api.Application.Command.Role.UpdateRoleCommand,System.Threading.CancellationToken)">
            <summary>
            更新角色
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Application.Handler.User.CreateUserHandler.Handle(RBAC.Write.Api.Application.Command.User.CreateUserCommand,System.Threading.CancellationToken)">
            <summary>
            创建用户
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Application.Handler.User.DeleteUserHandler.Handle(RBAC.Write.Api.Application.Command.User.DeleteUserCommand,System.Threading.CancellationToken)">
            <summary>
            删除用户
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Application.Handler.User.UpdateUserEnableHandler.Handle(RBAC.Write.Api.Application.Command.User.UpdateUserEnableCommand,System.Threading.CancellationToken)">
            <summary>
            /修改用户状态
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Application.Handler.User.UpdateUserHandler.Handle(RBAC.Write.Api.Application.Command.User.UpdateUserCommand,System.Threading.CancellationToken)">
            <summary>
            更新用户
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Controllers.RBACController.CreatePermission(RBAC.Write.Api.Application.Command.Permission.CreatePermissionCommand)">
            <summary>
            添加权限
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Controllers.RBACController.CreateRoleC(RBAC.Write.Api.Application.Command.Role.CreateRoleCommand,System.Threading.CancellationToken)">
            <summary>
            创建角色
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Controllers.RBACController.CreateUser(RBAC.Write.Api.Application.Command.User.CreateUserCommand)">
            <summary>
            创建用户
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Controllers.RBACController.UpdatePermission(RBAC.Write.Api.Application.Command.Permission.UpdatePermissionCommand)">
            <summary>
            修改权限
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Controllers.RBACController.UpdateRole(RBAC.Write.Api.Application.Command.Role.UpdateRoleCommand)">
            <summary>
            更新角色
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Controllers.RBACController.UpdateUser(RBAC.Write.Api.Application.Command.User.UpdateUserCommand)">
            <summary>
            更新用户
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Controllers.RBACController.DeletePermission(RBAC.Write.Api.Application.Command.Permission.DeletePermissionCommand)">
            <summary>
            删除权限
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Controllers.RBACController.DeleteRole(RBAC.Write.Api.Application.Command.Role.DeleteRoleCommand)">
            <summary>
            删除角色
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Controllers.RBACController.DeleteUser(RBAC.Write.Api.Application.Command.User.DeleteUserCommand)">
            <summary>
            删除用户
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Controllers.RBACController.UpdateRoleEnable(RBAC.Write.Api.Application.Command.Role.UpdateRoleEnableCommand)">
            <summary>
            修改角色状态
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Write.Api.Controllers.RBACController.UpdateUserEnable(RBAC.Write.Api.Application.Command.User.UpdateUserEnableCommand)">
            <summary>
            /修改用户状态
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
    </members>
</doc>
