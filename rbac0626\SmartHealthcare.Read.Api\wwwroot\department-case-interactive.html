<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科室病例管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            display: flex;
            height: 100vh;
            gap: 10px;
            padding: 10px;
        }

        .left-panel {
            width: 300px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .right-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .department-info, .case-info {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }

        .department-info {
            flex: 1;
        }

        .case-info {
            height: 200px;
            overflow-y: auto;
        }

        .panel-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            font-size: 16px;
            font-weight: bold;
        }

        .department-list {
            max-height: calc(100vh - 120px);
            overflow-y: auto;
        }

        .department-item {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .department-item:hover {
            background-color: #f8f9ff;
            transform: translateX(5px);
        }

        .department-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .department-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #ff6b6b;
        }

        .department-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .department-code {
            font-size: 12px;
            opacity: 0.7;
            margin-bottom: 5px;
        }

        .department-meta {
            font-size: 12px;
            opacity: 0.8;
        }

        .emergency-badge {
            display: inline-block;
            background: #ff4757;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            margin-left: 10px;
        }

        .info-card {
            background: #f8f9ff;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 0 8px 8px 0;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .info-label {
            font-weight: bold;
            color: #666;
            min-width: 80px;
        }

        .info-value {
            color: #333;
            flex: 1;
            text-align: right;
        }

        .case-item {
            background: #f8f9ff;
            border: 1px solid #e1e8ff;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .case-item:hover {
            background: #e8f0ff;
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }

        .case-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .case-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .case-meta {
            font-size: 12px;
            opacity: 0.7;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .error {
            color: #ff4757;
            text-align: center;
            padding: 20px;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }

        .search-box {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
        }

        .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .stats-bar {
            background: #f8f9ff;
            padding: 10px 20px;
            border-bottom: 1px solid #eee;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧科室列表 -->
        <div class="left-panel">
            <div class="panel-header">
                科室列表
            </div>
            <div class="search-box">
                <input type="text" class="search-input" placeholder="搜索科室..." id="searchInput">
            </div>
            <div class="stats-bar">
                共 <span id="departmentCount">0</span> 个科室
            </div>
            <div class="department-list" id="departmentList">
                <div class="loading">正在加载科室数据...</div>
            </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="right-panel">
            <!-- 科室详细信息 -->
            <div class="department-info">
                <h2 style="margin-bottom: 20px; color: #667eea;">科室详细信息</h2>
                <div id="departmentDetails">
                    <div class="empty-state">
                        <i>🏥</i>
                        <p>请点击左侧科室查看详细信息</p>
                    </div>
                </div>
                
                <!-- 科室病例列表 -->
                <div style="margin-top: 30px;">
                    <h3 style="margin-bottom: 15px; color: #667eea;">科室病例</h3>
                    <div id="departmentCases">
                        <div class="empty-state">
                            <i>📋</i>
                            <p>请选择科室查看病例列表</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 病例详细信息 -->
            <div class="case-info">
                <h3 style="margin-bottom: 15px; color: #667eea;">病例详细信息</h3>
                <div id="caseDetails">
                    <div class="empty-state">
                        <i>📄</i>
                        <p>请点击上方病例查看详细信息</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class DepartmentCaseManager {
            constructor() {
                this.departments = [];
                this.cases = [];
                this.selectedDepartment = null;
                this.selectedCase = null;
                this.init();
            }

            async init() {
                await this.loadDepartments();
                await this.loadCases();
                this.setupEventListeners();
                this.renderDepartments();
            }

            async loadDepartments() {
                try {
                    const response = await fetch('/api/SmartHealthcare/GetDepartment');
                    const result = await response.json();
                    if (result.code === 200) {
                        this.departments = result.data || [];
                    } else {
                        throw new Error(result.msg || '加载科室数据失败');
                    }
                } catch (error) {
                    console.error('加载科室数据失败:', error);
                    document.getElementById('departmentList').innerHTML = 
                        '<div class="error">加载科室数据失败: ' + error.message + '</div>';
                }
            }

            async loadCases() {
                try {
                    const response = await fetch('/api/SmartHealthcare/GetDepartmentCase');
                    const result = await response.json();
                    if (result.code === 200) {
                        this.cases = result.data || [];
                    } else {
                        throw new Error(result.msg || '加载病例数据失败');
                    }
                } catch (error) {
                    console.error('加载病例数据失败:', error);
                }
            }

            setupEventListeners() {
                const searchInput = document.getElementById('searchInput');
                searchInput.addEventListener('input', (e) => {
                    this.filterDepartments(e.target.value);
                });
            }

            renderDepartments(filter = '') {
                const departmentList = document.getElementById('departmentList');
                const departmentCount = document.getElementById('departmentCount');
                
                let filteredDepartments = this.departments;
                if (filter) {
                    filteredDepartments = this.departments.filter(dept => 
                        dept.departmentName.includes(filter) || 
                        dept.departmentCode.includes(filter)
                    );
                }

                departmentCount.textContent = filteredDepartments.length;

                if (filteredDepartments.length === 0) {
                    departmentList.innerHTML = '<div class="empty-state"><i>🔍</i><p>未找到匹配的科室</p></div>';
                    return;
                }

                const html = filteredDepartments.map(dept => `
                    <div class="department-item" data-id="${dept.id}" onclick="manager.selectDepartment(${dept.id})">
                        <div class="department-name">
                            ${dept.departmentName}
                            ${dept.isEmergency ? '<span class="emergency-badge">急诊</span>' : ''}
                        </div>
                        <div class="department-code">编码: ${dept.departmentCode}</div>
                        <div class="department-meta">
                            ${dept.address} | 挂号费: ¥${dept.registrationMoney}
                        </div>
                    </div>
                `).join('');

                departmentList.innerHTML = html;
            }

            filterDepartments(filter) {
                this.renderDepartments(filter);
            }

            selectDepartment(departmentId) {
                // 更新选中状态
                document.querySelectorAll('.department-item').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelector(`[data-id="${departmentId}"]`).classList.add('active');

                this.selectedDepartment = this.departments.find(d => d.id === departmentId);
                this.selectedCase = null; // 清除病例选择

                this.renderDepartmentDetails();
                this.renderDepartmentCases();
                this.clearCaseDetails();
            }

            renderDepartmentDetails() {
                const detailsContainer = document.getElementById('departmentDetails');
                if (!this.selectedDepartment) {
                    detailsContainer.innerHTML = '<div class="empty-state"><i>🏥</i><p>请点击左侧科室查看详细信息</p></div>';
                    return;
                }

                const dept = this.selectedDepartment;
                const html = `
                    <div class="info-card">
                        <div class="info-row">
                            <span class="info-label">科室名称:</span>
                            <span class="info-value">${dept.departmentName}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">科室编码:</span>
                            <span class="info-value">${dept.departmentCode}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">科室地址:</span>
                            <span class="info-value">${dept.address}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">挂号费:</span>
                            <span class="info-value">¥${dept.registrationMoney}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">科室类型:</span>
                            <span class="info-value">${dept.isEmergency ? '急诊科室' : '普通科室'}</span>
                        </div>
                    </div>
                `;
                detailsContainer.innerHTML = html;
            }

            renderDepartmentCases() {
                const casesContainer = document.getElementById('departmentCases');
                if (!this.selectedDepartment) {
                    casesContainer.innerHTML = '<div class="empty-state"><i>📋</i><p>请选择科室查看病例列表</p></div>';
                    return;
                }

                const departmentCases = this.cases.filter(c => c.departmentId === this.selectedDepartment.id);
                
                if (departmentCases.length === 0) {
                    casesContainer.innerHTML = '<div class="empty-state"><i>📋</i><p>该科室暂无病例</p></div>';
                    return;
                }

                const html = departmentCases.map(caseItem => `
                    <div class="case-item" data-case-id="${caseItem.id}" onclick="manager.selectCase('${caseItem.caseName}', ${caseItem.departmentId})">
                        <div class="case-name">${caseItem.caseName}</div>
                        <div class="case-meta">科室: ${caseItem.departmentName}</div>
                    </div>
                `).join('');

                casesContainer.innerHTML = html;
            }

            selectCase(caseName, departmentId) {
                // 更新选中状态
                document.querySelectorAll('.case-item').forEach(item => {
                    item.classList.remove('active');
                });
                event.currentTarget.classList.add('active');

                this.selectedCase = { caseName, departmentId };
                this.renderCaseDetails();
            }

            renderCaseDetails() {
                const detailsContainer = document.getElementById('caseDetails');
                if (!this.selectedCase) {
                    detailsContainer.innerHTML = '<div class="empty-state"><i>📄</i><p>请点击上方病例查看详细信息</p></div>';
                    return;
                }

                const department = this.departments.find(d => d.id === this.selectedCase.departmentId);
                const html = `
                    <div class="info-card">
                        <div class="info-row">
                            <span class="info-label">病例名称:</span>
                            <span class="info-value">${this.selectedCase.caseName}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">所属科室:</span>
                            <span class="info-value">${department ? department.departmentName : '未知'}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">科室编码:</span>
                            <span class="info-value">${department ? department.departmentCode : '未知'}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">科室地址:</span>
                            <span class="info-value">${department ? department.address : '未知'}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">挂号费:</span>
                            <span class="info-value">¥${department ? department.registrationMoney : '0'}</span>
                        </div>
                    </div>
                `;
                detailsContainer.innerHTML = html;
            }

            clearCaseDetails() {
                document.getElementById('caseDetails').innerHTML = 
                    '<div class="empty-state"><i>📄</i><p>请点击上方病例查看详细信息</p></div>';
            }
        }

        // 初始化管理器
        const manager = new DepartmentCaseManager();
    </script>
</body>
</html>
