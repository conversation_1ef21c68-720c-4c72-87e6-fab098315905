﻿using MediatR;
using RBAC.Domain.Dto.Output;
using RBAC.ErrorCode;

namespace RBAC.Read.Api.Application.Command.User.Login
{
    public class LoginCommand:IRequest<APIResult<UserDto>>
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; }
        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }
    }
}
