{"format": 1, "restore": {"D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Interstructrue\\SmartHealthcare.Interstructrue.csproj": {}}, "projects": {"D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Domain\\SmartHealthcare.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Domain\\SmartHealthcare.Domain.csproj", "projectName": "SmartHealthcare.Domain", "projectPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Domain\\SmartHealthcare.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DEv1\\Components\\Offline Packages", "D:\\新建文件夹 (2)\\333\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "E:\\DEv1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Interstructrue\\SmartHealthcare.Interstructrue.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Interstructrue\\SmartHealthcare.Interstructrue.csproj", "projectName": "SmartHealthcare.Interstructrue", "projectPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Interstructrue\\SmartHealthcare.Interstructrue.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Interstructrue\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DEv1\\Components\\Offline Packages", "D:\\新建文件夹 (2)\\333\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "E:\\DEv1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Domain\\SmartHealthcare.Domain.csproj": {"projectPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Domain\\SmartHealthcare.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}