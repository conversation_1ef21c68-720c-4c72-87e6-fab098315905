<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RBAC.Read.Api</name>
    </assembly>
    <members>
        <member name="P:RBAC.Read.Api.Application.Command.Permission.GetPermissionListCommand.PageIndex">
            <summary>
            页码
            </summary>
        </member>
        <member name="P:RBAC.Read.Api.Application.Command.Permission.GetPermissionListCommand.PageSize">
            <summary>
            每页数量
            </summary>
        </member>
        <member name="P:RBAC.Read.Api.Application.Command.Permission.GetPermissionListCommand.PermissionName">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="P:RBAC.Read.Api.Application.Command.Role.GetRoleListCommand.PageIndex">
            <summary>
            页码
            </summary>
        </member>
        <member name="P:RBAC.Read.Api.Application.Command.Role.GetRoleListCommand.PageSize">
            <summary>
            每页数量
            </summary>
        </member>
        <member name="P:RBAC.Read.Api.Application.Command.Role.GetRoleListCommand.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="T:RBAC.Read.Api.Application.Command.User.GetUserCommand">
            <summary>
            获取用户列表
            </summary>
        </member>
        <member name="P:RBAC.Read.Api.Application.Command.User.Login.LoginCommand.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:RBAC.Read.Api.Application.Command.User.Login.LoginCommand.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="M:RBAC.Read.Api.Application.Handler.Permission.GetPermissionHandler.Handle(RBAC.Read.Api.Application.Command.Permission.GetPermissionCommand,System.Threading.CancellationToken)">
            <summary>
            获取权限列表
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Read.Api.Application.Handler.Permission.GetPermissionListHandler.Handle(RBAC.Read.Api.Application.Command.Permission.GetPermissionListCommand,System.Threading.CancellationToken)">
            <summary>
            获取权限列表
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Read.Api.Application.Handler.Role.GetRoleHandler.Handle(RBAC.Read.Api.Application.Command.Role.GetRoleCommand,System.Threading.CancellationToken)">
            <summary>
            获取角色列表
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Read.Api.Application.Handler.Role.GetRoleListHandler.Handle(RBAC.Read.Api.Application.Command.Role.GetRoleListCommand,System.Threading.CancellationToken)">
            <summary>
            获取角色列表
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Read.Api.Application.Handler.User.GetUserHandler.Handle(RBAC.Read.Api.Application.Command.User.GetUserCommand,System.Threading.CancellationToken)">
            
        </member>
        <member name="M:RBAC.Read.Api.Application.Handler.User.GetUserListHandler.Handle(RBAC.Read.Api.Application.Command.User.GetUserListCommand,System.Threading.CancellationToken)">
            <summary>
            获取用户列表
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Read.Api.Application.Handler.User.Login.LoginHandler.Handle(RBAC.Read.Api.Application.Command.User.Login.LoginCommand,System.Threading.CancellationToken)">
            <summary>
            登录
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Read.Api.Controllers.RBACController.Login(RBAC.Read.Api.Application.Command.User.Login.LoginCommand)">
            <summary>
            登录
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Read.Api.Controllers.RBACController.GetPermission(RBAC.Read.Api.Application.Command.Permission.GetPermissionCommand,System.Threading.CancellationToken)">
            <summary>
            获取权限列表
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Read.Api.Controllers.RBACController.GetPermissionList(RBAC.Read.Api.Application.Command.Permission.GetPermissionListCommand)">
            <summary>
            获取权限列表
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Read.Api.Controllers.RBACController.GetRoleList(RBAC.Read.Api.Application.Command.Role.GetRoleListCommand)">
            <summary>
            获取角色列表
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Read.Api.Controllers.RBACController.GetUserList(RBAC.Read.Api.Application.Command.User.GetUserListCommand)">
            <summary>
            获取用户列表
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:RBAC.Read.Api.Controllers.RBACController.GetRole(RBAC.Read.Api.Application.Command.Role.GetRoleCommand)">
            <summary>
            获取角色列表
            </summary>
            <param name="request"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
    </members>
</doc>
