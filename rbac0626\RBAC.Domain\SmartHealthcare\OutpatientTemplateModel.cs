﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RBAC.Domain.SmartHealthcare
{
    /// <summary>
    /// 门诊单模板
    /// </summary>
    public class OutpatientTemplateModel:BaseEntity
    {
        /// <summary>
        /// 门诊单编号
        /// </summary>
        public string OutpatientTemplateCode { get; set; } = null!;
        /// <summary>
        /// 门诊单模板
        /// </summary>
        public string OutpatientTemplate { get; set; } = null!;
        ///// <summary>
        ///// 单据类型：(处方单 / 检查单)
        ///// </summary>
        //public string BillType { get; set; }
        ///// <summary>
        ///// 内容规则(如药品用法用量)
        ///// </summary>
        //public string ContentRule { get; set; }
        ///// <summary>
        ///// 收费项目Id
        ///// </summary>
        //public  long ChargeItemId { get; set; }
        ///// <summary>
        ///// 自动推送至药房 0:不推送 1:推送
        ///// </summary>
        //public int AutoPush { get; set; }
        ///// <summary>
        ///// 挂号单模板
        ///// </summary>
        //[Column(TypeName = "json")]
        //public string OutpatientTemplates{ get; set; }

    }
    /// <summary>
    /// 门诊单模板字段
    /// </summary>
    public class OutpatientTemplate
    {
        /// <summary>
        /// 字段
        /// </summary>
        public string Field { get; set; }
        /// <summary>
        /// 标签
        /// </summary>
        public string Label { get; set; }
        /// <summary>
        /// 值
        /// </summary>
        public string Value { get; set; }
    }
}
