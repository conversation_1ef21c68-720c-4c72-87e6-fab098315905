[{"ContainingType": "RBAC.Read.Api.Controllers.RBACController", "Method": "GetPermission", "RelativePath": "api/RBAC/GetPermission", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RBAC.Read.Api.Application.Command.Permission.GetPermissionCommand", "IsRequired": false}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Collections.Generic.List`1[[RBAC.Domain.Dto.Output.GetPermissionListDto, RBAC.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Read.Api.Controllers.RBACController", "Method": "GetPermissionList", "RelativePath": "api/RBAC/GetPermissionList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "PermissionName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIPaging`1[[RBAC.Domain.Dto.Output.PermissionDto, RBAC.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Read.Api.Controllers.RBACController", "Method": "GetRole", "RelativePath": "api/RBAC/GetRole", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RBAC.Read.Api.Application.Command.Role.GetRoleCommand", "IsRequired": false}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Collections.Generic.List`1[[RBAC.Domain.Dto.Output.GetRoleListDto, RBAC.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Read.Api.Controllers.RBACController", "Method": "GetRoleList", "RelativePath": "api/RBAC/GetRoleList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "RoleName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIPaging`1[[RBAC.Domain.Dto.Output.GetRoleListDto, RBAC.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Read.Api.Controllers.RBACController", "Method": "GetUser", "RelativePath": "api/RBAC/GetUser", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RBAC.Read.Api.Application.Command.User.GetUserCommand", "IsRequired": false}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Collections.Generic.List`1[[RBAC.Domain.Dto.Output.GetUserListDto, RBAC.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Read.Api.Controllers.RBACController", "Method": "GetUserList", "RelativePath": "api/RBAC/GetUserList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Pageindex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "UserName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIPaging`1[[RBAC.Domain.Dto.Output.GetUserListDto, RBAC.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Read.Api.Controllers.RBACController", "Method": "<PERSON><PERSON>", "RelativePath": "api/RBAC/Login", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserName", "Type": "System.String", "IsRequired": false}, {"Name": "Password", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[RBAC.Domain.Dto.Output.UserDto, RBAC.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Read.Api.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RBAC.Read.Api.WeatherForecast, RBAC.Read.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]