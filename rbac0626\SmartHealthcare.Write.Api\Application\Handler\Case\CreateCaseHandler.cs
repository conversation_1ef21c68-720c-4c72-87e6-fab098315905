﻿using AutoMapper;
using MediatR;
using RBAC.Domain.SmartHealthcare;
using RBAC.ErrorCode;
using RBAC.Interstructrue.Base;
using SmartHealthcare.Write.Api.Application.Command.Case;

namespace SmartHealthcare.Write.Api.Application.Handler.Case
{
    /// <summary>
    /// /**
    /// 创建病例
    /// **
    /// </summary>
    public class CreateCaseHandler : IRequestHandler<CreateCaseCommand, APIResult<int>>
    {
        private readonly IBaseRepository<CaseModel> caseRepository;
        private readonly IMapper mapper;
        private readonly ILogger<CreateCaseHandler> logger;
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="caseRepository"></param>
        /// <param name="mapper"></param>
        /// <param name="logger"></param>
        public CreateCaseHandler(IBaseRepository<CaseModel> caseRepository, IMapper mapper, ILogger<CreateCaseHandler> logger)
        {
            this.caseRepository = caseRepository;
            this.mapper = mapper;
            this.logger = logger;
        }
        /// <summary>
        /// 添加病例
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public Task<APIResult<int>> Handle(CreateCaseCommand request, CancellationToken cancellationToken)
        {
            APIResult<int> res = new APIResult<int>();
            var model = mapper.Map<CaseModel>(request);
            model.CreateTime = DateTime.Now;
            var r = caseRepository.Create(model);
            if (r > 0)
            {
                res.Data = r;
                res.Code = APIEnums.Success;
                res.Msg = "添加成功";
                logger.LogInformation("添加病例成功");
                return Task.FromResult(res);
            }
            else
            {
                res.Code = APIEnums.Fail;
                res.Msg = "添加失败";
                res.Data = r;
                logger.LogError("添加病例失败");
                return Task.FromResult(res);
            }
        }
    }
}
