﻿using AutoMapper;
using RBAC.Domain.RBAC;
using RBAC.Write.Api.Application.Command.Permission;
using RBAC.Write.Api.Application.Command.Role;
using RBAC.Write.Api.Application.Command.User;

namespace RBAC.Write.Api
{
    public class RBACAutoMapper : Profile
    {
        public RBACAutoMapper()
        {
            CreateMap<CreateUserCommand, UserModel>();
            CreateMap<CreateRoleCommand, RoleModel>();
            CreateMap<CreatePermissionCommand, PermissionModel>();
            CreateMap<UpdatePermissionCommand, PermissionModel>();
            CreateMap<UpdateRoleCommand, RoleModel>();
            CreateMap<UpdateUserCommand, UserModel>();
        }
    }
}
