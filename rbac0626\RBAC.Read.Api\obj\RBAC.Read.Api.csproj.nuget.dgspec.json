{"format": 1, "restore": {"D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Read.Api\\RBAC.Read.Api.csproj": {}}, "projects": {"D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Domain\\RBAC.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Domain\\RBAC.Domain.csproj", "projectName": "RBAC.Domain", "projectPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Domain\\RBAC.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DEv1\\Components\\Offline Packages", "D:\\新建文件夹 (2)\\333\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "E:\\DEv1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.ErrorCode\\RBAC.ErrorCode.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.ErrorCode\\RBAC.ErrorCode.csproj", "projectName": "RBAC.ErrorCode", "projectPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.ErrorCode\\RBAC.ErrorCode.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.ErrorCode\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DEv1\\Components\\Offline Packages", "D:\\新建文件夹 (2)\\333\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "E:\\DEv1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Interstructrue\\RBAC.Interstructrue.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Interstructrue\\RBAC.Interstructrue.csproj", "projectName": "RBAC.Interstructrue", "projectPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Interstructrue\\RBAC.Interstructrue.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Interstructrue\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DEv1\\Components\\Offline Packages", "D:\\新建文件夹 (2)\\333\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "E:\\DEv1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Domain\\RBAC.Domain.csproj": {"projectPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Domain\\RBAC.Domain.csproj"}, "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.ErrorCode\\RBAC.ErrorCode.csproj": {"projectPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.ErrorCode\\RBAC.ErrorCode.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Read.Api\\RBAC.Read.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Read.Api\\RBAC.Read.Api.csproj", "projectName": "RBAC.Read.Api", "projectPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Read.Api\\RBAC.Read.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Read.Api\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DEv1\\Components\\Offline Packages", "D:\\新建文件夹 (2)\\333\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "E:\\DEv1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Domain\\RBAC.Domain.csproj": {"projectPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Domain\\RBAC.Domain.csproj"}, "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.ErrorCode\\RBAC.ErrorCode.csproj": {"projectPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.ErrorCode\\RBAC.ErrorCode.csproj"}, "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Interstructrue\\RBAC.Interstructrue.csproj": {"projectPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Interstructrue\\RBAC.Interstructrue.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "IGeekFan.AspNetCore.Knife4jUI": {"target": "Package", "version": "[0.0.16, )"}, "MD5": {"target": "Package", "version": "[2.2.5, )"}, "MediatR": {"target": "Package", "version": "[12.5.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "NLog": {"target": "Package", "version": "[6.0.0, )"}, "NLog.Config": {"target": "Package", "version": "[4.7.15, )"}, "NLog.Web.AspNetCore": {"target": "Package", "version": "[6.0.0, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}, "Yitter.IdGenerator": {"target": "Package", "version": "[1.0.14, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}