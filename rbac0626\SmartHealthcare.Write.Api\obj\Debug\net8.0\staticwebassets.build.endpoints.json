{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "_content/SmartHealthcare.Read.Api/department-case-demo.html", "AssetFile": "_content/SmartHealthcare.Read.Api/department-case-demo.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8141"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI=\""}, {"Name": "Last-Modified", "Value": "Wed, 30 Jul 2025 13:03:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI="}]}, {"Route": "_content/SmartHealthcare.Read.Api/department-case-demo.zvet8wci5s.html", "AssetFile": "_content/SmartHealthcare.Read.Api/department-case-demo.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8141"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI=\""}, {"Name": "Last-Modified", "Value": "Wed, 30 Jul 2025 13:03:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zvet8wci5s"}, {"Name": "integrity", "Value": "sha256-YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI="}, {"Name": "label", "Value": "_content/SmartHealthcare.Read.Api/department-case-demo.html"}]}, {"Route": "_content/SmartHealthcare.Read.Api/department-case-interactive.html", "AssetFile": "_content/SmartHealthcare.Read.Api/department-case-interactive.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17964"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 01 Aug 2025 01:02:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE="}]}, {"Route": "_content/SmartHealthcare.Read.Api/department-case-interactive.xeaab6lj4y.html", "AssetFile": "_content/SmartHealthcare.Read.Api/department-case-interactive.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "17964"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 01 Aug 2025 01:02:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xeaab6lj4y"}, {"Name": "integrity", "Value": "sha256-OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE="}, {"Name": "label", "Value": "_content/SmartHealthcare.Read.Api/department-case-interactive.html"}]}]}