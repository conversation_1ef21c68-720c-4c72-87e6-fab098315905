namespace SmartHealthcare.Read.Api.DTo
{
    /// <summary>
    /// 科室及其病例信息DTO（用于左侧科室列表显示）
    /// </summary>
    public class DepartmentWithCasesDto
    {
        /// <summary>
        /// 科室ID
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 科室编码
        /// </summary>
        public string DepartmentCode { get; set; } = string.Empty;
        
        /// <summary>
        /// 科室名称
        /// </summary>
        public string DepartmentName { get; set; } = string.Empty;
        
        /// <summary>
        /// 科室地址
        /// </summary>
        public string Address { get; set; } = string.Empty;
        
        /// <summary>
        /// 挂号费
        /// </summary>
        public decimal RegistrationMoney { get; set; }
        
        /// <summary>
        /// 是否急诊 1 是 0 否
        /// </summary>
        public int IsEmergency { get; set; }
        
        /// <summary>
        /// 该科室下的病例列表
        /// </summary>
        public List<CaseDto> Cases { get; set; } = new List<CaseDto>();
        
        /// <summary>
        /// 病例数量
        /// </summary>
        public int CaseCount => Cases?.Count ?? 0;
    }
}
