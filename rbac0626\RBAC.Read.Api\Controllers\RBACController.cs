﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using RBAC.Domain.Dto.Output;
using RBAC.ErrorCode;
using RBAC.Read.Api.Application.Command.Permission;
using RBAC.Read.Api.Application.Command.Role;
using RBAC.Read.Api.Application.Command.User;
using RBAC.Read.Api.Application.Command.User.Login;

namespace RBAC.Read.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class RBACController : ControllerBase
    {
        private readonly IMediator mediator;

        public RBACController(IMediator mediator)
        {
            this.mediator = mediator;
        }
        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet]
        public Task<APIResult<UserDto>> Login([FromQuery]LoginCommand request)
        {
            return mediator.Send(request);
        }

        /// <summary>
        /// 获取权限列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet]
        public Task<APIResult<List<GetPermissionListDto>>> GetPermission([FromQuery]GetPermissionCommand request, CancellationToken cancellationToken)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 获取权限列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet]
        public Task<APIPaging<PermissionDto>> GetPermissionList([FromQuery]GetPermissionListCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 获取角色列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet]
        public Task<APIPaging<GetRoleListDto>> GetRoleList([FromQuery]GetRoleListCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 获取用户列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet]
        public Task<APIPaging<GetUserListDto>> GetUserList([FromQuery]GetUserListCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 获取角色列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet]
        public Task<APIResult<List<GetRoleListDto>>> GetRole([FromQuery]GetRoleCommand request)
        {
            return mediator.Send(request);
        }
    }
}
