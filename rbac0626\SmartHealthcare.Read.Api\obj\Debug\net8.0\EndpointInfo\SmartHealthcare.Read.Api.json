{"openapi": "3.0.1", "info": {"title": "SmartHealthcare.Read.Api", "version": "1.0"}, "paths": {"/api/SmartHealthcare/GetDepartment": {"get": {"tags": ["SmartHealthcare"], "parameters": [{"name": "request", "in": "query", "schema": {"$ref": "#/components/schemas/GetDepartmentCommand"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DepartmentDtoListAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DepartmentDtoListAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DepartmentDtoListAPIResult"}}}}}}}, "/api/SmartHealthcare/GetDepartmentWithCases": {"get": {"tags": ["SmartHealthcare"], "parameters": [{"name": "request", "in": "query", "schema": {"$ref": "#/components/schemas/GetDepartmentWithCasesCommand"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DepartmentWithCasesDtoListAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DepartmentWithCasesDtoListAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DepartmentWithCasesDtoListAPIResult"}}}}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"APIEnums": {"enum": [200, 500, 502], "type": "integer", "format": "int32"}, "CaseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "departmentId": {"type": "integer", "format": "int64"}, "caseName": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "createUser": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DepartmentDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "departmentCode": {"type": "string", "nullable": true}, "departmentName": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "registrationMoney": {"type": "number", "format": "double"}, "isEmergency": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DepartmentDtoListAPIResult": {"type": "object", "properties": {"msg": {"type": "string", "nullable": true}, "code": {"$ref": "#/components/schemas/APIEnums"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DepartmentDto"}, "nullable": true}}, "additionalProperties": false}, "DepartmentWithCasesDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "departmentCode": {"type": "string", "nullable": true}, "departmentName": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "registrationMoney": {"type": "number", "format": "double"}, "isEmergency": {"type": "integer", "format": "int32"}, "cases": {"type": "array", "items": {"$ref": "#/components/schemas/CaseDto"}, "nullable": true}, "caseCount": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "DepartmentWithCasesDtoListAPIResult": {"type": "object", "properties": {"msg": {"type": "string", "nullable": true}, "code": {"$ref": "#/components/schemas/APIEnums"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DepartmentWithCasesDto"}, "nullable": true}}, "additionalProperties": false}, "GetDepartmentCommand": {"type": "object", "additionalProperties": false}, "GetDepartmentWithCasesCommand": {"type": "object", "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}