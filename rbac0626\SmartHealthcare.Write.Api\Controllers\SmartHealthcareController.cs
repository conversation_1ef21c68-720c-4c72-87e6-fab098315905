﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using RBAC.ErrorCode;
using SmartHealthcare.Write.Api.Application.Command.Case;
using SmartHealthcare.Write.Api.Application.Command.Department;

namespace SmartHealthcare.Write.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class SmartHealthcareController : ControllerBase
    {
        private readonly IMediator mediator;

        public SmartHealthcareController(IMediator mediator)
        {
            this.mediator = mediator;
        }
        /// <summary>
        /// 创建科室
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost]
        public Task<APIResult<int>> CreateDepartment(CreateDepartmentCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 添加病例
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        [HttpPost]
        public Task<APIResult<int>> CreateCase(CreateCaseCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 删除病例
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpDelete]
        public Task<APIResult<int>> DeleteCase(DeleteCaseCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 删除科室
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpDelete]
        public Task<APIResult<int>> DeleteDepartmentC(DeleteDepartmentCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 更新科室信息
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost]
        public Task<APIResult<int>> UpdateDepartment(UpdateDepartmentCommand request)
        {
            return mediator.Send(request);
        }

    }
}
