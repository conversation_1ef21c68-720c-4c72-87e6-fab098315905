using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using RBAC.Domain.SmartHealthcare;
using RBAC.ErrorCode;
using RBAC.Interstructrue;
using RBAC.Interstructrue.Base;
using SmartHealthcare.Read.Api.Application.Command.Department;
using SmartHealthcare.Read.Api.Application.Handler.Department;
using SmartHealthcare.Read.Api.DTo;
using Xunit;

namespace SmartHealthcare.Read.Api.Tests
{
    /// <summary>
    /// GetDepartmentCaseHandler 集成测试
    /// </summary>
    public class GetDepartmentCaseHandlerIntegrationTests : IDisposable
    {
        private readonly RBACDbContext _context;
        private readonly IBaseRepository<CaseModel> _caseRepository;
        private readonly IBaseRepository<DepartmentModel> _departmentRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ILogger<GetDepartmentCaseHandler>> _mockLogger;
        private readonly GetDepartmentCaseHandler _handler;

        public GetDepartmentCaseHandlerIntegrationTests()
        {
            // 使用内存数据库进行测试
            var options = new DbContextOptionsBuilder<RBACDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new RBACDbContext(options);
            _caseRepository = new BaseRepository<CaseModel>(_context);
            _departmentRepository = new BaseRepository<DepartmentModel>(_context);
            _mockMapper = new Mock<IMapper>();
            _mockLogger = new Mock<ILogger<GetDepartmentCaseHandler>>();

            _handler = new GetDepartmentCaseHandler(
                _caseRepository,
                _departmentRepository,
                _mockMapper.Object,
                _mockLogger.Object
            );

            // 初始化测试数据
            SeedTestData();
        }

        private void SeedTestData()
        {
            var departments = new List<DepartmentModel>
            {
                new DepartmentModel
                {
                    Id = 1,
                    DepartmentCode = "001",
                    DepartmentName = "内科",
                    Address = "1楼",
                    RegistrationMoney = 10.00m,
                    IsEmergency = 0,
                    IsDeleted = false,
                    CreateTime = DateTime.Now,
                    CreateUser = "system"
                },
                new DepartmentModel
                {
                    Id = 2,
                    DepartmentCode = "002",
                    DepartmentName = "急诊科",
                    Address = "2楼",
                    RegistrationMoney = 20.00m,
                    IsEmergency = 1,
                    IsDeleted = false,
                    CreateTime = DateTime.Now,
                    CreateUser = "system"
                },
                new DepartmentModel
                {
                    Id = 3,
                    DepartmentCode = "003",
                    DepartmentName = "外科",
                    Address = "3楼",
                    RegistrationMoney = 15.00m,
                    IsEmergency = 0,
                    IsDeleted = true, // 已删除的科室
                    CreateTime = DateTime.Now,
                    CreateUser = "system"
                }
            };

            var cases = new List<CaseModel>
            {
                new CaseModel
                {
                    Id = 1,
                    DepartmentId = 1,
                    CaseName = "感冒",
                    IsDeleted = false,
                    CreateTime = DateTime.Now,
                    CreateUser = "system"
                },
                new CaseModel
                {
                    Id = 2,
                    DepartmentId = 1,
                    CaseName = "发烧",
                    IsDeleted = false,
                    CreateTime = DateTime.Now,
                    CreateUser = "system"
                },
                new CaseModel
                {
                    Id = 3,
                    DepartmentId = 2,
                    CaseName = "急性胃炎",
                    IsDeleted = false,
                    CreateTime = DateTime.Now,
                    CreateUser = "system"
                },
                new CaseModel
                {
                    Id = 4,
                    DepartmentId = 1,
                    CaseName = "头痛",
                    IsDeleted = true, // 已删除的病例
                    CreateTime = DateTime.Now,
                    CreateUser = "system"
                }
            };

            _context.Set<DepartmentModel>().AddRange(departments);
            _context.Set<CaseModel>().AddRange(cases);
            _context.SaveChanges();
        }

        [Fact]
        public async Task Handle_WithNullRequest_ShouldReturnFailResult()
        {
            // Arrange
            GetDepartmentCaseCommand? request = null;

            // Act
            var result = await _handler.Handle(request!, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Fail, result.Code);
            Assert.Equal("请求参数不能为空", result.Msg);
            Assert.NotNull(result.Data);
            Assert.Empty(result.Data);
        }

        [Fact]
        public async Task Handle_WithValidRequest_ShouldReturnSuccessResult()
        {
            // Arrange
            var request = new GetDepartmentCaseCommand();

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Success, result.Code);
            Assert.Equal("查询成功", result.Msg);
            Assert.NotNull(result.Data);
            Assert.True(result.Data.Count > 0);
        }

        [Fact]
        public async Task Handle_WithDepartmentIdFilter_ShouldFilterCorrectly()
        {
            // Arrange
            var request = new GetDepartmentCaseCommand
            {
                DepartmentId = 1
            };

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Success, result.Code);
            Assert.NotNull(result.Data);
            Assert.All(result.Data, item => Assert.Equal(1, item.DepartmentId));
        }

        [Fact]
        public async Task Handle_WithEmergencyOnlyFilter_ShouldFilterCorrectly()
        {
            // Arrange
            var request = new GetDepartmentCaseCommand
            {
                IsEmergencyOnly = true
            };

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Success, result.Code);
            Assert.NotNull(result.Data);
            Assert.All(result.Data, item => Assert.Equal(1, item.IsEmergency));
        }

        [Fact]
        public async Task Handle_WithDepartmentNameFilter_ShouldFilterCorrectly()
        {
            // Arrange
            var request = new GetDepartmentCaseCommand
            {
                DepartmentName = "内科"
            };

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Success, result.Code);
            Assert.NotNull(result.Data);
            Assert.All(result.Data, item => Assert.Contains("内科", item.DepartmentName));
        }

        [Fact]
        public async Task Handle_WithCaseNameFilter_ShouldFilterCorrectly()
        {
            // Arrange
            var request = new GetDepartmentCaseCommand
            {
                CaseName = "感冒"
            };

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Success, result.Code);
            Assert.NotNull(result.Data);
            Assert.All(result.Data, item => Assert.Contains("感冒", item.CaseName));
        }

        [Fact]
        public async Task Handle_WithPagination_ShouldReturnCorrectPageSize()
        {
            // Arrange
            var request = new GetDepartmentCaseCommand
            {
                PageNumber = 1,
                PageSize = 2
            };

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Success, result.Code);
            Assert.NotNull(result.Data);
            Assert.True(result.Data.Count <= 2);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        public async Task Handle_WithInvalidPageNumber_ShouldReturnFailResult(int pageNumber)
        {
            // Arrange
            var request = new GetDepartmentCaseCommand
            {
                PageNumber = pageNumber,
                PageSize = 10
            };

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Fail, result.Code);
            Assert.Equal("页码必须大于0", result.Msg);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        [InlineData(1001)]
        public async Task Handle_WithInvalidPageSize_ShouldReturnFailResult(int pageSize)
        {
            // Arrange
            var request = new GetDepartmentCaseCommand
            {
                PageNumber = 1,
                PageSize = pageSize
            };

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Fail, result.Code);
            Assert.Equal("每页大小必须在1-1000之间", result.Msg);
        }

        [Fact]
        public async Task Handle_ShouldExcludeDeletedRecords()
        {
            // Arrange
            var request = new GetDepartmentCaseCommand();

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Success, result.Code);
            Assert.NotNull(result.Data);
            
            // 确保没有返回已删除的科室（外科，ID=3）
            Assert.DoesNotContain(result.Data, item => item.DepartmentName == "外科");
            
            // 确保没有返回已删除的病例（头痛）
            Assert.DoesNotContain(result.Data, item => item.CaseName == "头痛");
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
