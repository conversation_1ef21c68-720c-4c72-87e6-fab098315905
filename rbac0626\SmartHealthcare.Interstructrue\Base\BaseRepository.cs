﻿using SmartHealthcare.Domain;

namespace SmartHealthcare.Interstructrue.Base
{
    public class BaseRepository<T> : IBaseRepository<T> where T : BaseEntity
    {
        private readonly SmartHealthcareDbContext db;

        public BaseRepository(SmartHealthcareDbContext db)
        {
            this.db = db;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public int Create(T entity)
        {
            //异常处理
            try
            {
                db.Set<T>().Add(entity);
                return db.SaveChanges();
            }
            catch (Exception)
            {

                throw;
            }
        }
        /// <summary>
        /// 表达式获取数据
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public IQueryable<T> GetALL()
        {
            //异常处理
            try
            {
                return db.Set<T>();
            }
            catch (Exception)
            {

                throw;
            }
        }
        /// <summary>
        /// ID获取数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public T GetModel(long id)
        {
            //异常处理
            try
            {
                return db.Set<T>().Find(id);
            }
            catch (Exception)
            {

                throw;
            }
        }
        /// <summary>
        /// 修改删除
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public int Update(T entity)
        {
            //异常处理
            try
            {
                db.Set<T>().Update(entity);
                return db.SaveChanges();
            }
            catch (Exception)
            {

                throw;
            }
        }
    }
}
