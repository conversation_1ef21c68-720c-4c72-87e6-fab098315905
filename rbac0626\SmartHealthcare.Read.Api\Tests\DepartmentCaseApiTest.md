# 科室病例API测试指南

## 测试环境准备

1. 确保数据库中有测试数据：
   - 至少2-3个科室记录
   - 每个科室至少1-2个病例记录

2. 启动SmartHealthcare.Read.Api项目

## API测试用例

### 1. 测试获取所有科室信息

**请求**：
```
GET http://localhost:5000/api/SmartHealthcare/GetDepartment
```

**预期响应**：
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "id": 1,
      "departmentCode": "001",
      "departmentName": "内科",
      "address": "1楼",
      "registrationMoney": 10.00,
      "isEmergency": 0
    }
  ]
}
```

### 2. 测试科室病例两表连查

**请求**：
```
GET http://localhost:5000/api/SmartHealthcare/GetDepartmentCase
```

**预期响应**：
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "id": 1,
      "departmentCode": "001",
      "departmentName": "内科",
      "address": "1楼",
      "registrationMoney": 10.00,
      "isEmergency": 0,
      "departmentId": 1,
      "caseName": "感冒"
    }
  ]
}
```

### 3. 测试获取科室及其病例分组信息

**请求**：
```
GET http://localhost:5000/api/SmartHealthcare/GetDepartmentWithCases
```

**预期响应**：
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "id": 1,
      "departmentCode": "001",
      "departmentName": "内科",
      "address": "1楼",
      "registrationMoney": 10.00,
      "isEmergency": 0,
      "cases": [
        {
          "id": 1,
          "departmentId": 1,
          "caseName": "感冒",
          "createTime": "2024-01-01T10:00:00",
          "createUser": "system"
        }
      ],
      "caseCount": 1
    }
  ]
}
```

### 4. 测试根据科室ID获取病例

**请求**：
```
GET http://localhost:5000/api/SmartHealthcare/GetCasesByDepartment?departmentId=1
```

**预期响应**：
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "id": 1,
      "departmentId": 1,
      "caseName": "感冒",
      "createTime": "2024-01-01T10:00:00",
      "createUser": "system"
    }
  ]
}
```

## 前端演示测试

1. 访问：`http://localhost:5000/department-case-demo.html`

2. 验证功能：
   - 左侧显示科室列表
   - 每个科室显示病例数量
   - 点击科室后右侧显示对应病例
   - 界面响应正常，无错误信息

## 错误场景测试

### 1. 测试不存在的科室ID

**请求**：
```
GET http://localhost:5000/api/SmartHealthcare/GetCasesByDepartment?departmentId=999
```

**预期响应**：
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": []
}
```

### 2. 测试无效参数

**请求**：
```
GET http://localhost:5000/api/SmartHealthcare/GetCasesByDepartment?departmentId=abc
```

**预期响应**：
```json
{
  "code": 500,
  "msg": "查询失败",
  "data": []
}
```

## 性能测试建议

1. 测试大量数据场景（100+科室，1000+病例）
2. 测试并发访问
3. 监控数据库查询性能
4. 检查内存使用情况

## 常见问题排查

1. **API返回空数据**：
   - 检查数据库连接
   - 确认数据库中有未删除的记录（IsDeleted = false）

2. **前端页面无法加载**：
   - 确认静态文件服务已启用
   - 检查浏览器控制台错误信息

3. **CORS错误**：
   - 如果前端和API在不同端口，需要配置CORS策略
