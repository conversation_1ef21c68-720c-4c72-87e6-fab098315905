[{"ContainingType": "RBAC.Write.Api.Controllers.RBACController", "Method": "CreatePermission", "RelativePath": "api/RBAC/CreatePermission", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RBAC.Write.Api.Application.Command.Permission.CreatePermissionCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Write.Api.Controllers.RBACController", "Method": "CreateRoleC", "RelativePath": "api/RBAC/CreateRoleC", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RBAC.Write.Api.Application.Command.Role.CreateRoleCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Write.Api.Controllers.RBACController", "Method": "CreateUser", "RelativePath": "api/RBAC/CreateUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RBAC.Write.Api.Application.Command.User.CreateUserCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Write.Api.Controllers.RBACController", "Method": "DeletePermission", "RelativePath": "api/RBAC/DeletePermission", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RBAC.Write.Api.Application.Command.Permission.DeletePermissionCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Write.Api.Controllers.RBACController", "Method": "DeleteRole", "RelativePath": "api/RBAC/DeleteRole", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RBAC.Write.Api.Application.Command.Role.DeleteRoleCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Write.Api.Controllers.RBACController", "Method": "DeleteUser", "RelativePath": "api/RBAC/DeleteUser", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RBAC.Write.Api.Application.Command.User.DeleteUserCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Write.Api.Controllers.RBACController", "Method": "UpdatePermission", "RelativePath": "api/RBAC/UpdatePermission", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RBAC.Write.Api.Application.Command.Permission.UpdatePermissionCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Write.Api.Controllers.RBACController", "Method": "UpdateRole", "RelativePath": "api/RBAC/UpdateRole", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RBAC.Write.Api.Application.Command.Role.UpdateRoleCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Write.Api.Controllers.RBACController", "Method": "UpdateRoleEnable", "RelativePath": "api/RBAC/UpdateRoleEnable", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RBAC.Write.Api.Application.Command.Role.UpdateRoleEnableCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Write.Api.Controllers.RBACController", "Method": "UpdateUser", "RelativePath": "api/RBAC/UpdateUser", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RBAC.Write.Api.Application.Command.User.UpdateUserCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Write.Api.Controllers.RBACController", "Method": "UpdateUserEnable", "RelativePath": "api/RBAC/UpdateUserEnable", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RBAC.Write.Api.Application.Command.User.UpdateUserEnableCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RBAC.Write.Api.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RBAC.Write.Api.WeatherForecast, RBAC.Write.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]