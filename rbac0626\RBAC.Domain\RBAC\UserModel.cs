﻿namespace RBAC.Domain.RBAC
{
    /// <summary>
    /// 用户/员工表
    /// </summary>
    public class UserModel : BaseEntity
    {
        /// <summary>
        /// 工号
        /// </summary>
        public string WorkNumber { get; set; } = null!;
        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = null!;
        /// <summary>
        /// 用户密码
        /// </summary>
        public string Password { get; set; } = null!;
        /// <summary>
        ///用户姓名
        /// </summary>
        public string FullName { get; set; } = null!;
        /// <summary>
        /// 性别  true 男  false 女
        /// </summary>
        public bool Sex { get; set; } 
        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; } = null!;
        /// <summary>
        /// 手机号码
        /// </summary>
        public string Mobile { get; set; } = null!;
        /// <summary>
        /// 拼音缩写
        /// </summary>
        public string RenPing { get; set; } = null!;
        /// <summary>
        ///科室名称
        /// </summary>
        public long DepartmentId { get; set; }
        /// <summary>
        /// 挂号费
        /// </summary>
        public int? RegistrationFee { get; set; }
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnable { get; set; } = true;
    }
}
