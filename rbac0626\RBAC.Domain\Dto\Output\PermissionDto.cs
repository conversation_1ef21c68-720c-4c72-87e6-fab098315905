﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RBAC.Domain.Dto.Output
{
    public  class PermissionDto
    {
        public long Id { get; set; }
        /// <summary>
        /// 权限名称
        /// </summary>
        public string PermissionName { get; set; }
        /// <summary>
        /// 权限URL
        /// </summary>
        public string PermissionURL { get; set; }
        /// <summary>
        /// 父级权限ID
        /// </summary>
        public long ParentId { get; set; }
    }
}
