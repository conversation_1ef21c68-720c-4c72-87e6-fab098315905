namespace SmartHealthcare.Read.Api.DTo
{
    /// <summary>
    /// 病例DTO
    /// </summary>
    public class CaseDto
    {
        /// <summary>
        /// 病例ID
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 科室ID
        /// </summary>
        public long DepartmentId { get; set; }
        
        /// <summary>
        /// 病例名称
        /// </summary>
        public string CaseName { get; set; } = string.Empty;
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; } = string.Empty;
    }
}
