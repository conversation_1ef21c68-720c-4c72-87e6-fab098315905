﻿using MediatR;
using RBAC.Domain.Dto.Output;
using RBAC.ErrorCode;

namespace RBAC.Read.Api.Application.Command.Permission
{
    public class GetPermissionListCommand:IRequest<APIPaging<PermissionDto>>
    {
        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; } = 1;
        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; } = 10;
        /// <summary>
        /// 权限名称
        /// </summary>
        public string? PermissionName { get; set; }
    }
}
