﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RBAC.Domain.Dto.Output
{
    /// <summary>
    /// 获取角色列表dto
    /// </summary>
    public class GetRoleListDto
    {
        public long Id { get; set; }
        /// <summary>
        /// 角色Id
        /// </summary>
        public long RoleId { get; set; }
        /// <summary>
        /// 角色名称
        /// </summary>
        public string RoleName { get; set; }
        /// <summary>
        /// 权限Id
        /// </summary>
        public List<long> PermissionIds { get; set; }
        /// <summary>
        /// 权限名称  
        /// </summary>
        public List<string> PermissionNames { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnable { get; set; }
    }
}
