﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RBAC.Domain.SmartHealthcare
{
    /// <summary>
    /// 数据库备份记录
    /// </summary>
    public class BackupRecordModel : BaseEntity
    {
        /// <summary>
        /// 备份类型(全量 / 增量)
        /// </summary>
        public string BackupType { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }
        /// <summary>
        /// 存储路径
        /// </summary>
        public string StoragePath { get; set; } = null!;
        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; } = null!;
        /// <summary>
        /// 大小
        /// </summary>
        public decimal Size { get; set; }
    }
}
