﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RBAC.Domain.SmartHealthcare
{
    /// <summary>
    /// 医生排班
    /// </summary>
    public class DoctorScheduleModel : BaseEntity
    {
        /// <summary>
        /// 医生Id(用户ID)
        /// </summary>
        public long DoctorId { get; set; }
        /// <summary>
        /// 科室Id
        /// </summary>
        public long DepartmentId { get; set; }
        /// <summary>
        /// 工作日期
        /// </summary>
        public DateTime WorkDate { get; set; }
        /// <summary>
        /// 时间段
        /// </summary>
        public string TimeSlot { get; set; }
        /// <summary>
        /// 预约数
        /// </summary>
        public int Quota { get; set; }
        /// <summary>
        /// 剩余预约数   (预约数-挂号数)
        /// </summary>
        public int RemainingQuota { get; set; }
    }
}
