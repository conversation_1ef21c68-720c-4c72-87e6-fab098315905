<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="C:\Users\<USER>\.nuget\packages\nlog.config\4.7.15\contentFiles\any\any\nlog.config" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="IGeekFan.AspNetCore.Knife4jUI" Version="0.0.16" />
    <PackageReference Include="MD5" Version="2.2.5" />
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="NLog" Version="6.0.0" />
    <PackageReference Include="NLog.Config" Version="4.7.15" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="6.0.0" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="Yitter.IdGenerator" Version="1.0.14" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\RBAC.Domain\RBAC.Domain.csproj" />
    <ProjectReference Include="..\RBAC.ErrorCode\RBAC.ErrorCode.csproj" />
    <ProjectReference Include="..\RBAC.Interstructrue\RBAC.Interstructrue.csproj" />
  </ItemGroup>

</Project>
