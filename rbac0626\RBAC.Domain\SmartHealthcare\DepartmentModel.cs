﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RBAC.Domain.SmartHealthcare
{
    /// <summary>
    /// 科室
    /// </summary>
    public class DepartmentModel : BaseEntity
    {
        /// <summary>
        /// 科室编码
        /// </summary>
        public string DepartmentCode { get; set; }
        /// <summary>
        /// 科室名称
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 科室地址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 挂号费
        /// </summary>
        public decimal RegistrationMoney { get; set; }
        /// <summary>
        /// 是否急诊 1 是 0 否
        /// </summary>
        public int IsEmergency { get; set; }

    }
}
