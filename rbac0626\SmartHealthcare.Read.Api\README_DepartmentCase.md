# 科室病例管理API文档

## 概述

本文档描述了科室和病例两表连查的API实现，支持显示科室列表和对应的病例信息。

## API端点

### 1. 获取所有科室信息
```
GET /api/SmartHealthcare/GetDepartment
```
返回所有科室的基本信息。

### 2. 获取科室病例信息（两表连查）
```
GET /api/SmartHealthcare/GetDepartmentCase
```
返回科室和病例的连查结果，每条记录包含科室信息和对应的病例信息。

### 3. 获取科室及其病例分组信息
```
GET /api/SmartHealthcare/GetDepartmentWithCases?departmentId={id}
```
返回科室列表，每个科室包含其下属的所有病例。
- `departmentId` (可选): 指定科室ID，如果提供则只返回该科室信息

### 4. 根据科室ID获取病例列表
```
GET /api/SmartHealthcare/GetCasesByDepartment?departmentId={id}
```
根据科室ID获取该科室下的所有病例。
- `departmentId` (必需): 科室ID

## 数据模型

### DepartmentDto
```json
{
  "id": 1,
  "departmentCode": "001",
  "departmentName": "内科",
  "address": "1楼",
  "registrationMoney": 10.00,
  "isEmergency": 0
}
```

### CaseDto
```json
{
  "id": 1,
  "departmentId": 1,
  "caseName": "感冒",
  "createTime": "2024-01-01T10:00:00",
  "createUser": "system"
}
```

### DepartmentWithCasesDto
```json
{
  "id": 1,
  "departmentCode": "001",
  "departmentName": "内科",
  "address": "1楼",
  "registrationMoney": 10.00,
  "isEmergency": 0,
  "cases": [
    {
      "id": 1,
      "departmentId": 1,
      "caseName": "感冒",
      "createTime": "2024-01-01T10:00:00",
      "createUser": "system"
    }
  ],
  "caseCount": 1
}
```

### DepartmentCaseDto
```json
{
  "id": 1,
  "departmentCode": "001",
  "departmentName": "内科",
  "address": "1楼",
  "registrationMoney": 10.00,
  "isEmergency": 0,
  "departmentId": 1,
  "caseName": "感冒"
}
```

## 使用示例

### 前端实现示例

1. **加载科室列表**：
   ```javascript
   const response = await fetch('/api/SmartHealthcare/GetDepartmentWithCases');
   const result = await response.json();
   ```

2. **点击科室显示病例**：
   ```javascript
   const response = await fetch(`/api/SmartHealthcare/GetCasesByDepartment?departmentId=${departmentId}`);
   const result = await response.json();
   ```

### 演示页面

访问 `/department-case-demo.html` 可以查看完整的前端演示页面，展示了：
- 左侧科室列表显示
- 点击科室显示对应病例
- 病例数量统计
- 响应式布局

## 技术实现

### 架构模式
- 使用CQRS模式分离读写操作
- MediatR处理命令和查询
- AutoMapper进行对象映射
- Repository模式进行数据访问

### 关键文件
- `GetDepartmentCaseHandler.cs`: 两表连查处理器
- `GetDepartmentWithCasesHandler.cs`: 科室病例分组处理器
- `GetCasesByDepartmentHandler.cs`: 根据科室获取病例处理器
- `SmartHealthcareAutoMapper.cs`: 对象映射配置
- `SmartHealthcareController.cs`: API控制器

### 数据库表关系
- `Departments` 表：科室信息
- `Cases` 表：病例信息
- 关联关系：`Cases.DepartmentId` -> `Departments.Id`

## 注意事项

1. 所有查询都会过滤已删除的记录（`IsDeleted = false`）
2. API返回统一的响应格式，包含状态码、消息和数据
3. 支持异常处理和日志记录
4. 前端示例使用原生JavaScript，可根据需要改为Vue、React等框架
