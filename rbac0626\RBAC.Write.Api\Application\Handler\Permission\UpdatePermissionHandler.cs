﻿using AutoMapper;
using MediatR;
using RBAC.Domain.RBAC;
using RBAC.ErrorCode;
using RBAC.Interstructrue.Base;
using RBAC.Write.Api.Application.Command.Permission;
using System.Diagnostics.Eventing.Reader;

namespace RBAC.Write.Api.Application.Handler.Permission
{
    public class UpdatePermissionHandler : IRequestHandler<UpdatePermissionCommand, APIResult<int>>
    {
        private readonly IBaseRepository<PermissionModel> permissionRepository;
        private readonly IMapper mapper;
        private readonly ILogger<UpdatePermissionHandler> logger;

        public UpdatePermissionHandler(IBaseRepository<PermissionModel> permissionRepository, IMapper mapper, ILogger<UpdatePermissionHandler> logger)
        {
            this.permissionRepository = permissionRepository;
            this.mapper = mapper;
            this.logger = logger;
        }
        /// <summary>
        /// 修改权限
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public Task<APIResult<int>> Handle(UpdatePermissionCommand request, CancellationToken cancellationToken)
        {
            APIResult<int> res = new APIResult<int>();
            var model = permissionRepository.GetALL().FirstOrDefault(x => x.Id == request.Id);
            var data = mapper.Map(request, model);
            var r = permissionRepository.Update(data);
            if (r > 0)
            {
                res.Data = r;
                res.Code = APIEnums.Success;
                res.Msg = "修改成功";
                return Task.FromResult(res);
            }
            else
            {
                res.Code = APIEnums.Fail;
                res.Msg = "修改失败";
                return Task.FromResult(res);
            }

        }
    }
}
