﻿using MediatR;
using RBAC.Domain.RBAC;
using RBAC.ErrorCode;
using RBAC.Interstructrue.Base;
using RBAC.Write.Api.Application.Command.User;

namespace RBAC.Write.Api.Application.Handler.User
{
    public class UpdateUserEnableHandler : IRequestHandler<UpdateUserEnableCommand, APIResult<int>>
    {
        private readonly IBaseRepository<UserModel> _userRepository;
        private readonly ILogger<UpdateUserEnableHandler> _logger;

        public UpdateUserEnableHandler(IBaseRepository<UserModel> userRepository, ILogger<UpdateUserEnableHandler> logger)
        {
            _userRepository = userRepository;
            _logger = logger;
        }
        /// <summary>
        /// /修改用户状态
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public Task<APIResult<int>> Handle(UpdateUserEnableCommand request, CancellationToken cancellationToken)
        {
            APIResult<int> res =new APIResult<int>();
            var model = _userRepository.GetALL().FirstOrDefault(x => x.Id == request.Id);
            model.IsEnable = request.IsEnable;
            var r = _userRepository.Update(model);
            if (r > 0)
            {
                res.Code = APIEnums.Success;
                res.Msg = "修改成功";
                _logger.LogInformation("修改成功");
                return Task.FromResult(res);
            }
            else {
                res.Code = APIEnums.Fail;
                res.Msg = "修改失败";
                _logger.LogError("修改失败");
                return Task.FromResult(res);
            }

        }
    }
}
