﻿using Microsoft.EntityFrameworkCore;
using RBAC.Domain.RBAC;
using RBAC.Domain.SmartHealthcare;

namespace RBAC.Interstructrue
{
    public class RBACDbContext : DbContext
    {
        public RBACDbContext(DbContextOptions options) : base(options)
        {
        }

        protected RBACDbContext()
        {
        }
        public DbSet<UserModel> Users { get; set; }
        public DbSet<RoleModel> Roles { get; set; }
        public DbSet<PermissionModel> Permissions { get; set; }
        public DbSet<UserRoleModel> UserRoles { get; set; }
        public DbSet<RolePermissionModel> RolePermissions { get; set; }


        //SmartHealthcare
        public DbSet<BackupRecordModel> BackupRecords { get; set; }
        public DbSet<CaseModel> Cases { get; set; }
        public DbSet<DepartmentModel> Departments { get; set; }
        public DbSet<InpatientSettingModel> InpatientSettings { get; set; }
        public DbSet<OutpatientTemplateModel> OutpatientTemplates { get; set; }
        public DbSet<RegistrationTemplateModel> RegistrationTemplates { get; set; }

    }
}
