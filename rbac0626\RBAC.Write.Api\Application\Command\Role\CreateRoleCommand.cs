﻿using MediatR;
using RBAC.ErrorCode;

namespace RBAC.Write.Api.Application.Command.Role
{
    public class CreateRoleCommand:IRequest<APIResult<int>>
    {
        /// <summary>
        /// 角色名称
        /// </summary>
        public string RoleName  { get; set; }
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnable { get; set; } = true;
        /// <summary>
        /// 权限Ids 
        /// </summary>
        public List<long> PermissionIds { get; set; }
    }
}
