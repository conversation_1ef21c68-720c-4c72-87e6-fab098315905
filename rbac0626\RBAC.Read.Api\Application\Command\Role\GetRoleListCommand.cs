﻿using MediatR;
using RBAC.Domain.Dto.Output;
using RBAC.ErrorCode;

namespace RBAC.Read.Api.Application.Command.Role
{
    public class GetRoleListCommand:IRequest<APIPaging<GetRoleListDto>>
    {
        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; } = 1;
        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; } = 10;
        /// <summary>
        /// 角色名称
        /// </summary>
        public string? RoleName { get; set; }
    }
}
