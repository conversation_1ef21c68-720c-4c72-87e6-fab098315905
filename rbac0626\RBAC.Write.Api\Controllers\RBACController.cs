﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using RBAC.ErrorCode;
using RBAC.Write.Api.Application.Command.Permission;
using RBAC.Write.Api.Application.Command.Role;
using RBAC.Write.Api.Application.Command.User;

namespace RBAC.Write.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class RBACController : ControllerBase
    {
        private readonly IMediator mediator;

        public RBACController(IMediator mediator)
        {
            this.mediator = mediator;
        }
        /// <summary>
        /// 添加权限
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost]
        public Task<APIResult<int>> CreatePermission(CreatePermissionCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 创建角色
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost]
        public Task<APIResult<int>> CreateRoleC(CreateRoleCommand request, CancellationToken cancellationToken)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 创建用户
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost]
        public Task<APIResult<int>> CreateUser(CreateUserCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 修改权限
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPut]
        public Task<APIResult<int>> UpdatePermission(UpdatePermissionCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 更新角色
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPut]
        public Task<APIResult<int>> UpdateRole(UpdateRoleCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 更新用户
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPut]
        public Task<APIResult<int>> UpdateUser(UpdateUserCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 删除权限
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpDelete]
        public Task<APIResult<int>> DeletePermission(DeletePermissionCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 删除角色
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpDelete]
        public Task<APIResult<int>> DeleteRole(DeleteRoleCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 删除用户
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpDelete]
        public Task<APIResult<int>> DeleteUser(DeleteUserCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 修改角色状态
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPut]
        public Task<APIResult<int>> UpdateRoleEnable(UpdateRoleEnableCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// /修改用户状态
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPut]
        public Task<APIResult<int>> UpdateUserEnable(UpdateUserEnableCommand request)
        {
            return mediator.Send(request);
        }
    }
}
