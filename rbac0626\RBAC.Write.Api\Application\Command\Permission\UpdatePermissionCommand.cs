﻿using MediatR;
using RBAC.ErrorCode;

namespace RBAC.Write.Api.Application.Command.Permission
{
    public class UpdatePermissionCommand : IRequest<APIResult<int>>
    {
        public long Id { get; set; }
        /// <summary>
        /// 权限名称
        /// </summary>
        public string PermissionName { get; set; }
        /// <summary>
        /// 权限URL
        /// </summary>
        public string PermissionURL { get; set; }
        /// <summary>
        /// 父级权限ID
        /// </summary>
        public long ParentId { get; set; }
    }
}
