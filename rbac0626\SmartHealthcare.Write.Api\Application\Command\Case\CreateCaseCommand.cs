﻿using MediatR;
using RBAC.ErrorCode;

namespace SmartHealthcare.Write.Api.Application.Command.Case
{
    public class CreateCaseCommand:IRequest<APIResult<int>>
    {
        /// <summary>
        /// 科别  （科室ID)
        /// </summary>
        public long DepartmentId { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string CaseName { get; set; } = null!;
    }
}
