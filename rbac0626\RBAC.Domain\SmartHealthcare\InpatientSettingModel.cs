﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RBAC.Domain.SmartHealthcare
{
    /// <summary>
    /// 住院单设计
    /// </summary>
    public class InpatientSettingModel : BaseEntity
    {
        /// <summary>
        /// 住院单编号
        /// </summary>
        public string InpatientSettingCode { get; set; } = null!;
        /// <summary>
        /// 住院单模板
        /// </summary>
        public string InpatientSettingTempalte { get; set; } = null!;
        ///// <summary>
        ///// 床位费 床位费 / 天
        ///// </summary>
        //public decimal BedFee { get; set; }
        ///// <summary>
        ///// 护理等级 1-5级
        ///// </summary>
        //public string NursingLevel { get; set; }
        ///// <summary>
        ///// /押金提醒
        ///// </summary>
        //public decimal DepositWarn { get; set; }
        ///// <summary>
        ///// 住院费规则 费用计算规则
        ///// </summary> 
        //public string FeeRules { get; set; }
        ///// <summary>
        ///// 住院单模板
        ///// </summary>
        //[Column(TypeName = "json")]
        //public string InpatientSettingTemplates { get; set; }
    }
    /// <summary>
    /// 门诊单模板字段
    /// </summary>
    public class InpatientSettingTemplate
    {
        /// <summary>
        /// 字段
        /// </summary>
        public string Field { get; set; }
        /// <summary>
        /// 标签
        /// </summary>
        public string Label { get; set; }
        /// <summary>
        /// 值
        /// </summary>
        public string Value { get; set; }
    }
}
