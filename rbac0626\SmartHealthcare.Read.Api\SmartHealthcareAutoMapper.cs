﻿using AutoMapper;
using RBAC.Domain.Dto.Output;
using RBAC.Domain.RBAC;
using RBAC.Domain.SmartHealthcare;
using SmartHealthcare.Read.Api.DTo;

namespace SmartHealthcare.Read.Api
{
    public class SmartHealthcareAutoMapper:Profile
    {
        public SmartHealthcareAutoMapper()
        {
            CreateMap<DepartmentModel, DepartmentDto>();
            CreateMap<CaseModel, CaseDto>();

            // 为DepartmentCaseDto创建自定义映射
            CreateMap<DepartmentModel, DepartmentCaseDto>()
                .ForMember(dest => dest.DepartmentId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.CaseName, opt => opt.Ignore()); // 病例名称需要单独设置
        }
    }
}
