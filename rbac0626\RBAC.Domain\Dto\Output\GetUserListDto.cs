﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RBAC.Domain.Dto.Output
{
    /// <summary>
    /// 获取用户列表dto
    /// </summary>
    public class GetUserListDto
    {
        public long Id { get; set; }
        /// <summary>
        /// 工号
        /// </summary>
        public string WorkNumber { get; set; } = null!;
        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = null!;
        /// <summary>
        /// 用户密码
        /// </summary>
        public string Password { get; set; } = null!;
        /// <summary>
        ///用户姓名
        /// </summary>
        public string FullName { get; set; } = null!;
        /// <summary>
        /// 性别  true 男  false 女
        /// </summary>
        public bool Sex { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; } = null!;
        /// <summary>
        /// 手机号码
        /// </summary>
        public string Mobile { get; set; } = null!;
        /// <summary>
        /// 拼音缩写
        /// </summary>
        public string RenPing { get; set; } = null!;
        /// <summary>
        ///科室名称
        /// </summary>
        public long DepartmentId { get; set; }
        /// <summary>
        /// 挂号费
        /// </summary>
        public int? RegistrationFee { get; set; }
        /// <summary>
        /// 角色id
        /// </summary>
        public List<long> RoleId { get; set; }
        /// <summary>
        /// 角色名
        /// </summary>
        public List<string> RoleName { get; set; }
        /// <summary>
        /// 权限id
        /// </summary>
        public List<long> PermissionIds { get; set; }
        /// <summary>
        /// 权限名称
        /// </summary>
        public List<string> PermissionNames { get; set; }
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnable { get; set; }
    }
}
