﻿using AutoMapper;
using MediatR;
using RBAC.Domain.Dto.Output;
using RBAC.Domain.RBAC;
using RBAC.ErrorCode;
using RBAC.Interstructrue.Base;
using RBAC.Read.Api.Application.Command.Permission;

namespace RBAC.Read.Api.Application.Handler.Permission
{
    public class GetPermissionHandler : IRequestHandler<GetPermissionCommand, APIResult<List<GetPermissionListDto>>>
    {
        private readonly IBaseRepository<PermissionModel> permissionRepository;
        private readonly IMapper mapper;

        public GetPermissionHandler(IBaseRepository<PermissionModel> permissionRepository, IMapper mapper)
        {
            this.permissionRepository = permissionRepository;
            this.mapper = mapper;
        }
        /// <summary>
        /// 获取权限列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public Task<APIResult<List<GetPermissionListDto>>> Handle(GetPermissionCommand request, CancellationToken cancellationToken)
        {
            APIResult<List<GetPermissionListDto>> res = new APIResult<List<GetPermissionListDto>>();
            res.Code = APIEnums.Success;
            res.Data = GetTreee(0);
            res.Msg = "获取成功";
            return Task.FromResult(res);
        }

        private List<GetPermissionListDto> GetTreee(long parentId)
        {
            var permission = permissionRepository.GetALL().Where(x => x.ParentId == parentId).ToList();
            var dto = mapper.Map<List<GetPermissionListDto>>(permission);
            foreach (var item in dto)
            {
                item.Children = GetTreee(item.Id);
            }
            return dto;
        }
    }
}
