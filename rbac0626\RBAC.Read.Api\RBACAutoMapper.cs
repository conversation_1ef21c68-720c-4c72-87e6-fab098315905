﻿using AutoMapper;
using RBAC.Domain.Dto.Output;
using RBAC.Domain.RBAC;

namespace RBAC.Read.Api
{
    public class RBACAutoMapper:Profile
    {
        public RBACAutoMapper()
        {
            CreateMap<PermissionModel, GetPermissionListDto>();
            CreateMap<PermissionModel, PermissionDto>();
            CreateMap<UserModel, UserDto>();
            CreateMap<RoleModel, GetRoleListDto>();
        }
    }
}
