﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RBAC.Domain.SmartHealthcare
{
    /// <summary>
    /// 费用类型
    /// </summary>
    public class FeeTypeModel:BaseEntity
    {
        /// <summary>
        /// 费用类型名称
        /// </summary>
        public string TypeName { get; set; }
        /// <summary>
        /// 收费标准
        /// </summary>
        public decimal Standard { get; set; }
    }
}
