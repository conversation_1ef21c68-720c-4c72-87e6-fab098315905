{"openapi": "3.0.1", "info": {"title": "SmartHealthcare.Write.Api", "version": "1.0"}, "paths": {"/api/SmartHealthcare/CreateDepartment": {"post": {"tags": ["SmartHealthcare"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDepartmentCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateDepartmentCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateDepartmentCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}}}}}}, "/api/SmartHealthcare/CreateCase": {"post": {"tags": ["SmartHealthcare"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCaseCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCaseCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCaseCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}}}}}}, "/api/SmartHealthcare/DeleteCase": {"delete": {"tags": ["SmartHealthcare"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteCaseCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteCaseCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteCaseCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}}}}}}, "/api/SmartHealthcare/DeleteDepartmentC": {"delete": {"tags": ["SmartHealthcare"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteDepartmentCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteDepartmentCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteDepartmentCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}}}}}}, "/api/SmartHealthcare/UpdateDepartment": {"post": {"tags": ["SmartHealthcare"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDepartmentCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateDepartmentCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateDepartmentCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32APIResult"}}}}}}}, "/api/SmartHealthcare/GetDepartment": {"get": {"tags": ["SmartHealthcare"], "parameters": [{"name": "request", "in": "query", "schema": {"$ref": "#/components/schemas/GetDepartmentCommand"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DepartmentDtoListAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DepartmentDtoListAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DepartmentDtoListAPIResult"}}}}}}}, "/api/SmartHealthcare/GetDepartmentWithCases": {"get": {"tags": ["SmartHealthcare"], "parameters": [{"name": "request", "in": "query", "schema": {"$ref": "#/components/schemas/GetDepartmentWithCasesCommand"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DepartmentWithCasesDtoListAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DepartmentWithCasesDtoListAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DepartmentWithCasesDtoListAPIResult"}}}}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"APIEnums": {"enum": [200, 500, 502], "type": "integer", "format": "int32"}, "CaseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "departmentId": {"type": "integer", "format": "int64"}, "caseName": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "createUser": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateCaseCommand": {"type": "object", "properties": {"departmentId": {"type": "integer", "format": "int64"}, "caseName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateDepartmentCommand": {"type": "object", "properties": {"departmentCode": {"type": "string", "nullable": true}, "departmentName": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "registrationMoney": {"type": "number", "format": "double"}, "isEmergency": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DeleteCaseCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "DeleteDepartmentCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "DepartmentDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "departmentCode": {"type": "string", "nullable": true}, "departmentName": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "registrationMoney": {"type": "number", "format": "double"}, "isEmergency": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DepartmentDtoListAPIResult": {"type": "object", "properties": {"msg": {"type": "string", "nullable": true}, "code": {"$ref": "#/components/schemas/APIEnums"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DepartmentDto"}, "nullable": true}}, "additionalProperties": false}, "DepartmentWithCasesDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "departmentCode": {"type": "string", "nullable": true}, "departmentName": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "registrationMoney": {"type": "number", "format": "double"}, "isEmergency": {"type": "integer", "format": "int32"}, "cases": {"type": "array", "items": {"$ref": "#/components/schemas/CaseDto"}, "nullable": true}, "caseCount": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "DepartmentWithCasesDtoListAPIResult": {"type": "object", "properties": {"msg": {"type": "string", "nullable": true}, "code": {"$ref": "#/components/schemas/APIEnums"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DepartmentWithCasesDto"}, "nullable": true}}, "additionalProperties": false}, "GetDepartmentCommand": {"type": "object", "additionalProperties": false}, "GetDepartmentWithCasesCommand": {"type": "object", "additionalProperties": false}, "Int32APIResult": {"type": "object", "properties": {"msg": {"type": "string", "nullable": true}, "code": {"$ref": "#/components/schemas/APIEnums"}, "data": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateDepartmentCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "departmentCode": {"type": "string", "nullable": true}, "departmentName": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "registrationMoney": {"type": "number", "format": "double"}, "isEmergency": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}