using AutoMapper;
using MediatR;
using RBAC.Domain.Dto.Output;
using RBAC.Domain.RBAC;
using RBAC.ErrorCode;
using RBAC.Interstructrue.Base;
using RBAC.Read.Api.Application.Command.User;

namespace RBAC.Read.Api.Application.Handler.User
{
    public class GetUserHandler : IRequestHandler<GetUserCommand, APIResult<List<UserDto>>>
    {
        private readonly IBaseRepository<UserModel> userRepository;
        private readonly ILogger<GetUserHandler> logger;
        private readonly IBaseRepository<RoleModel> roleRepository;
        private readonly IBaseRepository<UserRoleModel> userRoleRepository;
        private readonly IBaseRepository<RolePermissionModel> rolePermissionRepository;
        private readonly IBaseRepository<PermissionModel> permissionRepository;
        private readonly IMapper mapper;

        public GetUserHandler(IBaseRepository<UserModel> userRepository, ILogger<GetUserHandler> logger, IBaseRepository<RoleModel> roleRepository, IBaseRepository<UserRoleModel> userRoleRepository, IBaseRepository<RolePermissionModel> rolePermissionRepository, IBaseRepository<PermissionModel> permissionRepository, IMapper mapper)
        {
            this.userRepository = userRepository;
            this.logger = logger;
            this.roleRepository = roleRepository;
            this.userRoleRepository = userRoleRepository;
            this.rolePermissionRepository = rolePermissionRepository;
            this.permissionRepository = permissionRepository;
            this.mapper = mapper;
        }
        ///
        public Task<APIResult<List<UserDto>>> Handle(GetUserCommand request, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }
    }
}
