﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
	<ItemGroup>
	<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
	<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0">
		<PrivateAssets>all</PrivateAssets>
		<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	</PackageReference>
	<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0">
		<PrivateAssets>all</PrivateAssets>
		<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	</PackageReference>
	<PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.0" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\SmartHealthcare.Domain\SmartHealthcare.Domain.csproj" />
	</ItemGroup>
</Project>
