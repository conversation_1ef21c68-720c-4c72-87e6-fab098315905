{"Version": 1, "WorkspaceRootPath": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{88143818-C72D-47FC-8607-DDEC6A4FEF02}|RBAC.Read.Api\\RBAC.Read.Api.csproj|d:\\实训\\one\\smarthealthcare\\rbac0626\\rbac.read.api\\application\\handler\\user\\getuserhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{88143818-C72D-47FC-8607-DDEC6A4FEF02}|RBAC.Read.Api\\RBAC.Read.Api.csproj|solutionrelative:rbac.read.api\\application\\handler\\user\\getuserhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{88143818-C72D-47FC-8607-DDEC6A4FEF02}|RBAC.Read.Api\\RBAC.Read.Api.csproj|d:\\实训\\one\\smarthealthcare\\rbac0626\\rbac.read.api\\controllers\\rbaccontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{88143818-C72D-47FC-8607-DDEC6A4FEF02}|RBAC.Read.Api\\RBAC.Read.Api.csproj|solutionrelative:rbac.read.api\\controllers\\rbaccontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{88143818-C72D-47FC-8607-DDEC6A4FEF02}|RBAC.Read.Api\\RBAC.Read.Api.csproj|d:\\实训\\one\\smarthealthcare\\rbac0626\\rbac.read.api\\application\\command\\user\\getusercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{88143818-C72D-47FC-8607-DDEC6A4FEF02}|RBAC.Read.Api\\RBAC.Read.Api.csproj|solutionrelative:rbac.read.api\\application\\command\\user\\getusercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{06D68DB3-AF0E-40B1-BC56-AE8E585DC40C}|RBAC.Domain\\RBAC.Domain.csproj|d:\\实训\\one\\smarthealthcare\\rbac0626\\rbac.domain\\rbac\\usermodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{06D68DB3-AF0E-40B1-BC56-AE8E585DC40C}|RBAC.Domain\\RBAC.Domain.csproj|solutionrelative:rbac.domain\\rbac\\usermodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{06D68DB3-AF0E-40B1-BC56-AE8E585DC40C}|RBAC.Domain\\RBAC.Domain.csproj|d:\\实训\\one\\smarthealthcare\\rbac0626\\rbac.domain\\rbac\\userrolemodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{06D68DB3-AF0E-40B1-BC56-AE8E585DC40C}|RBAC.Domain\\RBAC.Domain.csproj|solutionrelative:rbac.domain\\rbac\\userrolemodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E04AC3A7-0C99-438D-807C-B1B8E46499AA}|RBAC.ErrorCode\\RBAC.ErrorCode.csproj|d:\\实训\\one\\smarthealthcare\\rbac0626\\rbac.errorcode\\rbac.errorcode.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{E04AC3A7-0C99-438D-807C-B1B8E46499AA}|RBAC.ErrorCode\\RBAC.ErrorCode.csproj|solutionrelative:rbac.errorcode\\rbac.errorcode.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{eefa5220-e298-11d0-8f78-00a0c9110057}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "RBACController.cs", "DocumentMoniker": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Read.Api\\Controllers\\RBACController.cs", "RelativeDocumentMoniker": "RBAC.Read.Api\\Controllers\\RBACController.cs", "ToolTip": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Read.Api\\Controllers\\RBACController.cs", "RelativeToolTip": "RBAC.Read.Api\\Controllers\\RBACController.cs", "ViewState": "AgIAAFcAAAAAAAAAAAAUwGIAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T06:16:15.439Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "GetUserHandler.cs", "DocumentMoniker": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Read.Api\\Application\\Handler\\User\\GetUserHandler.cs", "RelativeDocumentMoniker": "RBAC.Read.Api\\Application\\Handler\\User\\GetUserHandler.cs", "ToolTip": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Read.Api\\Application\\Handler\\User\\GetUserHandler.cs", "RelativeToolTip": "RBAC.Read.Api\\Application\\Handler\\User\\GetUserHandler.cs", "ViewState": "AgIAADYAAAAAAAAAAAAIwDcAAABhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T05:50:13.987Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "GetUserCommand.cs", "DocumentMoniker": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Read.Api\\Application\\Command\\User\\GetUserCommand.cs", "RelativeDocumentMoniker": "RBAC.Read.Api\\Application\\Command\\User\\GetUserCommand.cs", "ToolTip": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Read.Api\\Application\\Command\\User\\GetUserCommand.cs", "RelativeToolTip": "RBAC.Read.Api\\Application\\Command\\User\\GetUserCommand.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAkwAkAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T05:49:19.328Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "UserModel.cs", "DocumentMoniker": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Domain\\RBAC\\UserModel.cs", "RelativeDocumentMoniker": "RBAC.Domain\\RBAC\\UserModel.cs", "ToolTip": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Domain\\RBAC\\UserModel.cs", "RelativeToolTip": "RBAC.Domain\\RBAC\\UserModel.cs", "ViewState": "AgIAACoAAAAAAAAAAAAEwDIAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T05:40:06.903Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "UserRoleModel.cs", "DocumentMoniker": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Domain\\RBAC\\UserRoleModel.cs", "RelativeDocumentMoniker": "RBAC.Domain\\RBAC\\UserRoleModel.cs", "ToolTip": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.Domain\\RBAC\\UserRoleModel.cs", "RelativeToolTip": "RBAC.Domain\\RBAC\\UserRoleModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T05:34:31.807Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "RBAC.ErrorCode", "DocumentMoniker": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.ErrorCode\\RBAC.ErrorCode.csproj", "RelativeDocumentMoniker": "RBAC.ErrorCode\\RBAC.ErrorCode.csproj", "ToolTip": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\RBAC.ErrorCode\\RBAC.ErrorCode.csproj", "RelativeToolTip": "RBAC.ErrorCode\\RBAC.ErrorCode.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-08-01T05:34:28.455Z", "EditorCaption": ""}]}]}]}