﻿
namespace SmartHealthcare.Read.Api.DTo
{
    public class DepartmentCaseDto
    {
        public long Id { get; set; }
        /// <summary>
        /// 科室编码
        /// </summary>
        public string DepartmentCode { get; set; } = string.Empty;
        /// <summary>
        /// 科室名称
        /// </summary>
        public string DepartmentName { get; set; } = string.Empty;
        /// <summary>
        /// 科室地址
        /// </summary>
        public string Address { get; set; } = string.Empty;
        /// <summary>
        /// 挂号费
        /// </summary>
        public decimal RegistrationMoney { get; set; }
        /// <summary>
        /// 是否急诊 1 是 0 否
        /// </summary>
        public int IsEmergency { get; set; }
        /// <summary>
        /// 科别  （科室ID)
        /// </summary>
        public long DepartmentId { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string CaseName { get; set; } = null!;
    }
}
