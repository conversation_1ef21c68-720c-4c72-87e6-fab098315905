﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RBAC.Domain.SmartHealthcare
{
    /// <summary>
    /// 病例
    /// </summary>
    public class CaseModel:BaseEntity
    {
        /// <summary>
        /// 科别  （科室ID)
        /// </summary>
        public long DepartmentId { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string CaseName { get; set; } = null!;
    }
}
