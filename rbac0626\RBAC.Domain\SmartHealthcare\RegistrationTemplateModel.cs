﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RBAC.Domain.SmartHealthcare
{
    /// <summary>
    /// 挂号单模版实体
    /// </summary>
    public class RegistrationTemplateModel:BaseEntity
    {
        /// <summary>
        /// 门诊单编号
        /// </summary>
        public string RegistrationTemplateCode { get; set; } = null!;
        /// <summary>
        /// 门诊单模板
        /// </summary>
        public string RegistrationTemplate { get; set; } = null!;
        ///// <summary>
        ///// 挂号单编号
        ///// </summary>
        //public string RegistrationCode { get; set; }
        ///// <summary>
        ///// 挂号类型  (普通 / 急诊)
        ///// </summary>
        //public string TypeName { get; set; }
        ///// <summary>
        ///// 收费类型Id
        ///// </summary>
        //public long FeeTypeId { get; set; }
        ///// <summary>
        ///// 是否支持医保 1 支持 0 不支持
        ///// </summary>
        //public int IsMedicalInsurance { get; set; }
        ///// <summary>
        ///// 挂号单模板
        ///// </summary>
        //[Column(TypeName = "json")]
        //public string RegistrationTemplates{ get; set; }


    }
    /// <summary>
    /// 挂号单模板字段
    /// </summary>
    public class RegistrationTemplate
    {
        /// <summary>
        /// 字段
        /// </summary>
        public string Field {  get; set; }
        /// <summary>
        /// 标签
        /// </summary>
        public string Label { get; set; }
        /// <summary>
        /// 值
        /// </summary>
        public string Value { get; set; }
    }

}
