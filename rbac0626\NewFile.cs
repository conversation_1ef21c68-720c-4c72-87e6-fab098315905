// 假设 roleRepository 用于获取角色数据，permissionRepository 用于获取权限数据
var roles = await roleRepository.GetAllAsync(); // 获取所有角色
var permissions = await permissionRepository.GetAllAsync(); // 获取所有权限

var result = roles.Select(role => new GetRoleListDto
{
    Id = role.Id,
    RoleId = role.RoleId,
    RoleName = role.RoleName,
    CreateTime = role.CreateTime,
    IsEnable = role.IsEnable,
    PermissionNames = permissions
        .Where(p => p.RoleId == role.RoleId)
        .Select(p => p.Name)
        .ToList()
}).ToList();