﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using RBAC.ErrorCode;
using SmartHealthcare.Read.Api.Application.Command.Case;
using SmartHealthcare.Read.Api.Application.Command.Department;
using SmartHealthcare.Read.Api.DTo;

namespace SmartHealthcare.Read.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class SmartHealthcareController : ControllerBase
    {
        private readonly IMediator _mediator;

        public SmartHealthcareController(IMediator mediator)
        {
            _mediator = mediator;
        }
        /// <summary>
        /// 获取所有科室信息
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet]
        public Task<APIResult<List<DepartmentDto>>> GetDepartment([FromQuery]GetDepartmentCommand request)
        {
            return _mediator.Send(request);
        }
        /// <summary>
        /// 获取科室及下级所有案例
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet]
        public Task<APIResult<List<DepartmentWithCasesDto>>> GetDepartmentWithCases([FromQuery] GetDepartmentWithCasesCommand request)
        {
            return _mediator.Send(request);
        }


    }
}
