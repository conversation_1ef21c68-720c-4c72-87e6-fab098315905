﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartHealthcare.Domain
{
    public class BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        public long Id { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }="system";
        /// <summary>
        /// 逻辑删除
        /// </summary>
        public bool IsDeleted { get; set; } = false;

    }
}
