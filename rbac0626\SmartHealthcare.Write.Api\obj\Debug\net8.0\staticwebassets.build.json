{"Version": 1, "Hash": "HdvWCBbkw9tqShO8uV195Zpx7jWIlLujsKyzJFhoEzg=", "Source": "SmartHealthcare.Write.Api", "BasePath": "_content/SmartHealthcare.Write.Api", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\SmartHealthcare.Read.Api.csproj", "Version": 2, "Source": "SmartHealthcare.Read.Api", "GetPublishAssetsTargets": "ComputeReferencedStaticWebAssetsPublishManifest;GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework"}], "DiscoveryPatterns": [{"Name": "SmartHealthcare.Read.Api\\wwwroot", "Source": "SmartHealthcare.Read.Api", "ContentRoot": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\", "BasePath": "_content/SmartHealthcare.Read.Api", "Pattern": "**"}], "Assets": [{"Identity": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\department-case-demo.html", "SourceId": "SmartHealthcare.Read.Api", "SourceType": "Project", "ContentRoot": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\", "BasePath": "_content/SmartHealthcare.Read.Api", "RelativePath": "department-case-demo#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zvet8wci5s", "Integrity": "YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\department-case-demo.html", "FileLength": 8141, "LastWriteTime": "2025-07-30T13:03:12+00:00"}, {"Identity": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\department-case-interactive.html", "SourceId": "SmartHealthcare.Read.Api", "SourceType": "Project", "ContentRoot": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\", "BasePath": "_content/SmartHealthcare.Read.Api", "RelativePath": "department-case-interactive#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xeaab6lj4y", "Integrity": "OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\department-case-interactive.html", "FileLength": 17964, "LastWriteTime": "2025-08-01T01:02:19+00:00"}], "Endpoints": [{"Route": "_content/SmartHealthcare.Read.Api/department-case-demo.html", "AssetFile": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\department-case-demo.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8141"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI=\""}, {"Name": "Last-Modified", "Value": "Wed, 30 Jul 2025 13:03:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI="}]}, {"Route": "_content/SmartHealthcare.Read.Api/department-case-demo.zvet8wci5s.html", "AssetFile": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\department-case-demo.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8141"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI=\""}, {"Name": "Last-Modified", "Value": "Wed, 30 Jul 2025 13:03:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zvet8wci5s"}, {"Name": "integrity", "Value": "sha256-YUcPXbjBR4LbaTcktnYyFqfInECt3ZF8uGfaks7d7bI="}, {"Name": "label", "Value": "_content/SmartHealthcare.Read.Api/department-case-demo.html"}]}, {"Route": "_content/SmartHealthcare.Read.Api/department-case-interactive.html", "AssetFile": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\department-case-interactive.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17964"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 01 Aug 2025 01:02:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE="}]}, {"Route": "_content/SmartHealthcare.Read.Api/department-case-interactive.xeaab6lj4y.html", "AssetFile": "D:\\实训\\one\\SmartHealthcare\\rbac0626\\SmartHealthcare.Read.Api\\wwwroot\\department-case-interactive.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "17964"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 01 Aug 2025 01:02:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xeaab6lj4y"}, {"Name": "integrity", "Value": "sha256-OSaojebTHOUg0rzpZFebabGPkKC6RbmpP8kWvD8LyJE="}, {"Name": "label", "Value": "_content/SmartHealthcare.Read.Api/department-case-interactive.html"}]}]}