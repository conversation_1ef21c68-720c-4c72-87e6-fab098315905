﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36212.18
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RBAC.ErrorCode", "RBAC.ErrorCode\RBAC.ErrorCode.csproj", "{E04AC3A7-0C99-438D-807C-B1B8E46499AA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RBAC.Domain", "RBAC.Domain\RBAC.Domain.csproj", "{06D68DB3-AF0E-40B1-BC56-AE8E585DC40C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RBAC.Interstructrue", "RBAC.Interstructrue\RBAC.Interstructrue.csproj", "{90DC4326-E53D-47BE-90FD-73FFE072F318}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RBAC.Read.Api", "RBAC.Read.Api\RBAC.Read.Api.csproj", "{88143818-C72D-47FC-8607-DDEC6A4FEF02}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RBAC.Write.Api", "RBAC.Write.Api\RBAC.Write.Api.csproj", "{AE1F075F-6974-4AC0-A85F-0A4C5715AC09}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "RBAC", "RBAC", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "SmartHealthcare", "SmartHealthcare", "{973ADE21-61E4-4E17-A594-6CEF83599535}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmartHealthcare.Write.Api", "SmartHealthcare.Write.Api\SmartHealthcare.Write.Api.csproj", "{9C0145FA-C2FA-40A9-97A5-DC5177AA4292}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmartHealthcare.Read.Api", "SmartHealthcare.Read.Api\SmartHealthcare.Read.Api.csproj", "{57DD6367-0968-4682-9A5D-5F528D085C01}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E04AC3A7-0C99-438D-807C-B1B8E46499AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E04AC3A7-0C99-438D-807C-B1B8E46499AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E04AC3A7-0C99-438D-807C-B1B8E46499AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E04AC3A7-0C99-438D-807C-B1B8E46499AA}.Release|Any CPU.Build.0 = Release|Any CPU
		{06D68DB3-AF0E-40B1-BC56-AE8E585DC40C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{06D68DB3-AF0E-40B1-BC56-AE8E585DC40C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{06D68DB3-AF0E-40B1-BC56-AE8E585DC40C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{06D68DB3-AF0E-40B1-BC56-AE8E585DC40C}.Release|Any CPU.Build.0 = Release|Any CPU
		{90DC4326-E53D-47BE-90FD-73FFE072F318}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{90DC4326-E53D-47BE-90FD-73FFE072F318}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{90DC4326-E53D-47BE-90FD-73FFE072F318}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{90DC4326-E53D-47BE-90FD-73FFE072F318}.Release|Any CPU.Build.0 = Release|Any CPU
		{88143818-C72D-47FC-8607-DDEC6A4FEF02}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{88143818-C72D-47FC-8607-DDEC6A4FEF02}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{88143818-C72D-47FC-8607-DDEC6A4FEF02}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{88143818-C72D-47FC-8607-DDEC6A4FEF02}.Release|Any CPU.Build.0 = Release|Any CPU
		{AE1F075F-6974-4AC0-A85F-0A4C5715AC09}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AE1F075F-6974-4AC0-A85F-0A4C5715AC09}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AE1F075F-6974-4AC0-A85F-0A4C5715AC09}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AE1F075F-6974-4AC0-A85F-0A4C5715AC09}.Release|Any CPU.Build.0 = Release|Any CPU
		{9C0145FA-C2FA-40A9-97A5-DC5177AA4292}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9C0145FA-C2FA-40A9-97A5-DC5177AA4292}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9C0145FA-C2FA-40A9-97A5-DC5177AA4292}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9C0145FA-C2FA-40A9-97A5-DC5177AA4292}.Release|Any CPU.Build.0 = Release|Any CPU
		{57DD6367-0968-4682-9A5D-5F528D085C01}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{57DD6367-0968-4682-9A5D-5F528D085C01}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{57DD6367-0968-4682-9A5D-5F528D085C01}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{57DD6367-0968-4682-9A5D-5F528D085C01}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{E04AC3A7-0C99-438D-807C-B1B8E46499AA} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{06D68DB3-AF0E-40B1-BC56-AE8E585DC40C} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{90DC4326-E53D-47BE-90FD-73FFE072F318} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{88143818-C72D-47FC-8607-DDEC6A4FEF02} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{AE1F075F-6974-4AC0-A85F-0A4C5715AC09} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{9C0145FA-C2FA-40A9-97A5-DC5177AA4292} = {973ADE21-61E4-4E17-A594-6CEF83599535}
		{57DD6367-0968-4682-9A5D-5F528D085C01} = {973ADE21-61E4-4E17-A594-6CEF83599535}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {2CB31DCE-23A1-40CE-8D4E-F695CEF7BFC9}
	EndGlobalSection
EndGlobal
