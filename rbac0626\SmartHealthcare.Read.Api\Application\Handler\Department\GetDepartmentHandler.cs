﻿using AutoMapper;
using MediatR;
using RBAC.Domain.SmartHealthcare;
using RBAC.ErrorCode;
using RBAC.Interstructrue.Base;
using SmartHealthcare.Read.Api.Application.Command.Department;
using SmartHealthcare.Read.Api.DTo;

namespace SmartHealthcare.Read.Api.Application.Handler.Department
{
    public class GetDepartmentHandler : IRequestHandler<GetDepartmentCommand, APIResult<List<DepartmentDto>>>
    {
        private readonly IBaseRepository<DepartmentModel> departRepository;
        private readonly IMapper mapper;
        private readonly ILogger<GetDepartmentHandler> logger;

        public GetDepartmentHandler(IBaseRepository<DepartmentModel> departRepository, IMapper mapper, ILogger<GetDepartmentHandler> logger)
        {
            this.departRepository = departRepository;
            this.mapper = mapper;
            this.logger = logger;
        }
        /// <summary>
        /// 获取所有科室信息
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public Task<APIResult<List<DepartmentDto>>> Handle(GetDepartmentCommand request, CancellationToken cancellationToken)
        {
            APIResult<List<DepartmentDto>> res =new APIResult<List<DepartmentDto>>();
            var model = departRepository.GetALL().Where(x => x.IsDeleted == false);
            var data = mapper.Map<List<DepartmentDto>>(model);
            res.Data = data;
            res.Code = APIEnums.Success;
            res.Msg = "查询成功";
            logger.LogInformation("查询成功");
            return Task.FromResult(res);

        }
    }
}
