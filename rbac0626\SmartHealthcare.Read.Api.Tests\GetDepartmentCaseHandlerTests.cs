using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using RBAC.Domain.SmartHealthcare;
using RBAC.ErrorCode;
using RBAC.Interstructrue.Base;
using SmartHealthcare.Read.Api.Application.Command.Department;
using SmartHealthcare.Read.Api.Application.Handler.Department;
using SmartHealthcare.Read.Api.DTo;
using Xunit;

namespace SmartHealthcare.Read.Api.Tests
{
    /// <summary>
    /// GetDepartmentCaseHandler 单元测试
    /// </summary>
    public class GetDepartmentCaseHandlerTests
    {
        private readonly Mock<IBaseRepository<CaseModel>> _mockCaseRepository;
        private readonly Mock<IBaseRepository<DepartmentModel>> _mockDepartmentRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ILogger<GetDepartmentCaseHandler>> _mockLogger;
        private readonly GetDepartmentCaseHandler _handler;

        public GetDepartmentCaseHandlerTests()
        {
            _mockCaseRepository = new Mock<IBaseRepository<CaseModel>>();
            _mockDepartmentRepository = new Mock<IBaseRepository<DepartmentModel>>();
            _mockMapper = new Mock<IMapper>();
            _mockLogger = new Mock<ILogger<GetDepartmentCaseHandler>>();

            _handler = new GetDepartmentCaseHandler(
                _mockCaseRepository.Object,
                _mockDepartmentRepository.Object,
                _mockMapper.Object,
                _mockLogger.Object
            );
        }

        [Fact]
        public async Task Handle_WithNullRequest_ShouldReturnFailResult()
        {
            // Arrange
            GetDepartmentCaseCommand? request = null;

            // Act
            var result = await _handler.Handle(request!, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Fail, result.Code);
            Assert.Equal("请求参数不能为空", result.Msg);
            Assert.NotNull(result.Data);
            Assert.Empty(result.Data);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        public async Task Handle_WithInvalidPageNumber_ShouldReturnFailResult(int pageNumber)
        {
            // Arrange
            var request = new GetDepartmentCaseCommand
            {
                PageNumber = pageNumber,
                PageSize = 10
            };

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Fail, result.Code);
            Assert.Equal("页码必须大于0", result.Msg);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        [InlineData(1001)]
        public async Task Handle_WithInvalidPageSize_ShouldReturnFailResult(int pageSize)
        {
            // Arrange
            var request = new GetDepartmentCaseCommand
            {
                PageNumber = 1,
                PageSize = pageSize
            };

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Fail, result.Code);
            Assert.Equal("每页大小必须在1-1000之间", result.Msg);
        }

        [Fact]
        public async Task Handle_WithValidRequest_ShouldReturnSuccessResult()
        {
            // Arrange
            var request = new GetDepartmentCaseCommand();

            var departments = new List<DepartmentModel>
            {
                new DepartmentModel
                {
                    Id = 1,
                    DepartmentCode = "001",
                    DepartmentName = "内科",
                    Address = "1楼",
                    RegistrationMoney = 10.00m,
                    IsEmergency = 0,
                    IsDeleted = false
                }
            }.AsQueryable();

            var cases = new List<CaseModel>
            {
                new CaseModel
                {
                    Id = 1,
                    DepartmentId = 1,
                    CaseName = "感冒",
                    IsDeleted = false
                }
            }.AsQueryable();

            _mockDepartmentRepository.Setup(x => x.GetALL()).Returns(departments);
            _mockCaseRepository.Setup(x => x.GetALL()).Returns(cases);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Success, result.Code);
            Assert.Equal("查询成功", result.Msg);
            Assert.NotNull(result.Data);
        }

        [Fact]
        public async Task Handle_WithDepartmentIdFilter_ShouldFilterCorrectly()
        {
            // Arrange
            var request = new GetDepartmentCaseCommand
            {
                DepartmentId = 1
            };

            var departments = new List<DepartmentModel>
            {
                new DepartmentModel { Id = 1, DepartmentName = "内科", IsDeleted = false },
                new DepartmentModel { Id = 2, DepartmentName = "外科", IsDeleted = false }
            }.AsQueryable();

            var cases = new List<CaseModel>().AsQueryable();

            _mockDepartmentRepository.Setup(x => x.GetALL()).Returns(departments);
            _mockCaseRepository.Setup(x => x.GetALL()).Returns(cases);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Success, result.Code);
            // 验证只返回了指定科室的数据
            Assert.All(result.Data, item => Assert.Equal(1, item.DepartmentId));
        }

        [Fact]
        public async Task Handle_WithEmergencyOnlyFilter_ShouldFilterCorrectly()
        {
            // Arrange
            var request = new GetDepartmentCaseCommand
            {
                IsEmergencyOnly = true
            };

            var departments = new List<DepartmentModel>
            {
                new DepartmentModel { Id = 1, DepartmentName = "内科", IsEmergency = 0, IsDeleted = false },
                new DepartmentModel { Id = 2, DepartmentName = "急诊科", IsEmergency = 1, IsDeleted = false }
            }.AsQueryable();

            var cases = new List<CaseModel>().AsQueryable();

            _mockDepartmentRepository.Setup(x => x.GetALL()).Returns(departments);
            _mockCaseRepository.Setup(x => x.GetALL()).Returns(cases);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Success, result.Code);
            // 验证只返回了急诊科室的数据
            Assert.All(result.Data, item => Assert.Equal(1, item.IsEmergency));
        }

        [Fact]
        public async Task Handle_WithPagination_ShouldReturnCorrectPageSize()
        {
            // Arrange
            var request = new GetDepartmentCaseCommand
            {
                PageNumber = 1,
                PageSize = 2
            };

            var departments = new List<DepartmentModel>
            {
                new DepartmentModel { Id = 1, DepartmentName = "内科", IsDeleted = false },
                new DepartmentModel { Id = 2, DepartmentName = "外科", IsDeleted = false },
                new DepartmentModel { Id = 3, DepartmentName = "儿科", IsDeleted = false }
            }.AsQueryable();

            var cases = new List<CaseModel>().AsQueryable();

            _mockDepartmentRepository.Setup(x => x.GetALL()).Returns(departments);
            _mockCaseRepository.Setup(x => x.GetALL()).Returns(cases);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(APIEnums.Success, result.Code);
            // 验证返回的数据量符合分页要求
            Assert.True(result.Data.Count <= 2);
        }
    }
}
