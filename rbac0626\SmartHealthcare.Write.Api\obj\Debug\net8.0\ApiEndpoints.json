[{"ContainingType": "SmartHealthcare.Write.Api.Controllers.SmartHealthcareController", "Method": "CreateCase", "RelativePath": "api/SmartHealthcare/CreateCase", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartHealthcare.Write.Api.Application.Command.Case.CreateCaseCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartHealthcare.Write.Api.Controllers.SmartHealthcareController", "Method": "CreateDepartment", "RelativePath": "api/SmartHealthcare/CreateDepartment", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartHealthcare.Write.Api.Application.Command.Department.CreateDepartmentCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartHealthcare.Write.Api.Controllers.SmartHealthcareController", "Method": "DeleteCase", "RelativePath": "api/SmartHealthcare/DeleteCase", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartHealthcare.Write.Api.Application.Command.Case.DeleteCaseCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartHealthcare.Write.Api.Controllers.SmartHealthcareController", "Method": "DeleteDepartmentC", "RelativePath": "api/SmartHealthcare/DeleteDepartmentC", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartHealthcare.Write.Api.Application.Command.Department.DeleteDepartmentCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartHealthcare.Read.Api.Controllers.SmartHealthcareController", "Method": "GetDepartment", "RelativePath": "api/SmartHealthcare/GetDepartment", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartHealthcare.Read.Api.Application.Command.Department.GetDepartmentCommand", "IsRequired": false}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Collections.Generic.List`1[[SmartHealthcare.Read.Api.DTo.DepartmentDto, SmartHealthcare.Read.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartHealthcare.Read.Api.Controllers.SmartHealthcareController", "Method": "GetDepartmentWithCases", "RelativePath": "api/SmartHealthcare/GetDepartmentWithCases", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartHealthcare.Read.Api.Application.Command.Department.GetDepartmentWithCasesCommand", "IsRequired": false}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Collections.Generic.List`1[[SmartHealthcare.Read.Api.DTo.DepartmentWithCasesDto, SmartHealthcare.Read.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartHealthcare.Write.Api.Controllers.SmartHealthcareController", "Method": "UpdateDepartment", "RelativePath": "api/SmartHealthcare/UpdateDepartment", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartHealthcare.Write.Api.Application.Command.Department.UpdateDepartmentCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "RBAC.ErrorCode.APIResult`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartHealthcare.Read.Api.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SmartHealthcare.Read.Api.WeatherForecast, SmartHealthcare.Read.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]