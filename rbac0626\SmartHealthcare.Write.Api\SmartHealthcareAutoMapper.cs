﻿using AutoMapper;
using RBAC.Domain.SmartHealthcare;
using SmartHealthcare.Write.Api.Application.Command.Case;
using SmartHealthcare.Write.Api.Application.Command.Department;

namespace SmartHealthcare.Write.Api
{
    /// <summary>
    /// 映射
    /// </summary>
    public class SmartHealthcareAutoMapper : Profile
    {
        /// <summary>
        /// 创建映射
        /// </summary>
        public SmartHealthcareAutoMapper()
        {
            CreateMap<CreateDepartmentCommand, DepartmentModel>().ReverseMap();
            CreateMap<UpdateDepartmentCommand, DepartmentModel>().ReverseMap();
            CreateMap<CreateCaseCommand, CaseModel>().ReverseMap();
        }
    }
}
